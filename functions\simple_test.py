"""
Simple test for the workitem_created function without complex imports.
This tests the core webhook processing logic.
"""

import json
from typing import Dict, Any, Optional


def extract_work_item_from_webhook(webhook_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Extract work item data from ADO webhook payload.
    """
    try:
        # ADO webhook structure varies, but typically has resource.fields
        resource = webhook_data.get('resource', {})
        fields = resource.get('fields', {})
        
        if not fields:
            return None
        
        # Extract common fields
        work_item_data = {
            'id': resource.get('id'),
            'title': fields.get('System.Title', ''),
            'description': fields.get('System.Description', ''),
            'work_item_type': fields.get('System.WorkItemType', ''),
            'state': fields.get('System.State', ''),
            'area_path': fields.get('System.AreaPath', ''),
            'iteration_path': fields.get('System.IterationPath', ''),
            'assigned_to': fields.get('System.AssignedTo', {}).get('displayName', '') if isinstance(fields.get('System.AssignedTo'), dict) else '',
            'created_by': fields.get('System.CreatedBy', {}).get('displayName', '') if isinstance(fields.get('System.CreatedBy'), dict) else '',
            'created_date': fields.get('System.CreatedDate', ''),
            'changed_date': fields.get('System.ChangedDate', ''),
            'priority': fields.get('Microsoft.VSTS.Common.Priority', 2),
            'severity': fields.get('Microsoft.VSTS.Common.Severity', ''),
            'tags': fields.get('System.Tags', ''),
            'repro_steps': fields.get('Microsoft.VSTS.TCM.ReproSteps', ''),
            'system_info': fields.get('Microsoft.VSTS.TCM.SystemInfo', ''),
        }
        
        return work_item_data
        
    except Exception as e:
        print(f"Error extracting work item from webhook: {e}")
        return None


def create_sample_ado_webhook_payload() -> Dict[str, Any]:
    """
    Create a sample Azure DevOps webhook payload for testing.
    This mimics what ADO sends when a work item is created.
    """
    return {
        "subscriptionId": "********-1234-1234-1234-************",
        "notificationId": 1,
        "id": "********-1234-1234-1234-************",
        "eventType": "workitem.created",
        "publisherId": "tfs",
        "message": {
            "text": "Bug #123 (Sample Bug) created by John Doe"
        },
        "detailedMessage": {
            "text": "Bug #123 (Sample Bug) created by John Doe"
        },
        "resource": {
            "id": 123,
            "workItemId": 123,
            "rev": 1,
            "revisedBy": {
                "displayName": "John Doe",
                "id": "********-1234-1234-1234-************"
            },
            "revisedDate": "2024-01-15T10:30:00.000Z",
            "fields": {
                "System.Id": 123,
                "System.Title": "Application crashes when clicking the submit button",
                "System.Description": "When users click the submit button on the contact form, the application crashes with a null reference exception. This happens consistently across different browsers.",
                "System.WorkItemType": "Bug",
                "System.State": "New",
                "System.AreaPath": "Air4 Channels Testing\\Frontend\\Forms",
                "System.IterationPath": "Air4 Channels Testing\\Sprint 1",
                "System.AssignedTo": "",
                "System.CreatedBy": {
                    "displayName": "John Doe",
                    "id": "********-1234-1234-1234-************"
                },
                "System.CreatedDate": "2024-01-15T10:30:00.000Z",
                "System.ChangedDate": "2024-01-15T10:30:00.000Z",
                "Microsoft.VSTS.Common.Priority": 2,
                "Microsoft.VSTS.Common.Severity": "2 - High",
                "System.Tags": "frontend; forms; crash",
                "Microsoft.VSTS.TCM.ReproSteps": "1. Navigate to contact form\n2. Fill in all required fields\n3. Click submit button\n4. Application crashes",
                "Microsoft.VSTS.TCM.SystemInfo": "Windows 10, Chrome 120.0.6099.109"
            }
        },
        "resourceVersion": "1.0",
        "resourceContainers": {
            "collection": {
                "id": "********-1234-1234-1234-************"
            },
            "account": {
                "id": "********-1234-1234-1234-************"
            },
            "project": {
                "id": "********-1234-1234-1234-************"
            }
        },
        "createdDate": "2024-01-15T10:30:00.000Z"
    }


def test_webhook_extraction():
    """
    Test the webhook payload extraction.
    """
    print("🧪 Testing Webhook Extraction for Virgin Atlantic ADO")
    print("=" * 60)
    
    # Create sample webhook payload
    webhook_payload = create_sample_ado_webhook_payload()
    
    print("📥 Sample webhook received:")
    print(f"   Event Type: {webhook_payload['eventType']}")
    print(f"   Work Item ID: {webhook_payload['resource']['id']}")
    print(f"   Project: Air4 Channels Testing")
    print()
    
    # Extract work item data
    work_item_data = extract_work_item_from_webhook(webhook_payload)
    
    if work_item_data:
        print("✅ Successfully extracted work item data:")
        print(f"   🆔 ID: {work_item_data['id']}")
        print(f"   📝 Title: {work_item_data['title']}")
        print(f"   🐛 Type: {work_item_data['work_item_type']}")
        print(f"   📍 State: {work_item_data['state']}")
        print(f"   🏢 Area Path: {work_item_data['area_path']}")
        print(f"   🔄 Iteration: {work_item_data['iteration_path']}")
        print(f"   ⚡ Priority: {work_item_data['priority']}")
        print(f"   🚨 Severity: {work_item_data['severity']}")
        print(f"   👤 Created By: {work_item_data['created_by']}")
        print(f"   📅 Created Date: {work_item_data['created_date']}")
        print(f"   🏷️  Tags: {work_item_data['tags']}")
        print()
        print("📋 Description:")
        print(f"   {work_item_data['description']}")
        print()
        print("🔧 Reproduction Steps:")
        print(f"   {work_item_data['repro_steps']}")
        print()
        print("💻 System Info:")
        print(f"   {work_item_data['system_info']}")
        print()
        
        # Simulate what the AI triage would do
        print("🤖 AI Triage Analysis (Simulated):")
        print("   🔍 Duplicate Detection: Searching for similar crashes...")
        print("   📊 Priority Analysis: High priority due to 'crash' keyword")
        print("   👥 Assignment: Would assign to Frontend team based on area path")
        print("   🔔 Teams Notification: Would notify #frontend-alerts channel")
        
        return True
    else:
        print("❌ Failed to extract work item data")
        return False


def simulate_function_response():
    """
    Simulate what the Azure Function would return.
    """
    print("\n🚀 Simulated Function Response:")
    print("=" * 40)
    
    response = {
        "status": "success",
        "work_item_id": 123,
        "triage_result": {
            "work_item_id": 123,
            "assigned_to": "<EMAIL>",
            "priority": 1,  # Elevated due to crash
            "duplicates": [],  # No duplicates found
            "confidence_score": 0.92,
            "reasoning": "High priority crash in frontend forms - assigned to frontend team based on area path and expertise"
        }
    }
    
    print(json.dumps(response, indent=2))
    return response


if __name__ == "__main__":
    # Test the webhook extraction
    success = test_webhook_extraction()
    
    if success:
        # Simulate the function response
        simulate_function_response()
        
        print("\n✅ Test completed successfully!")
        print("\n💡 Next Steps:")
        print("   1. Configure your Azure services (AI Search, Key Vault, etc.)")
        print("   2. Set up Azure DevOps Service Hook to point to your function")
        print("   3. Deploy the function to Azure")
        print("   4. Test with real work item creation")
    else:
        print("\n❌ Test failed!")
