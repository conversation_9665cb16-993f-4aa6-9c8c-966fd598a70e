{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "UseDevelopmentStorage=true", "FUNCTIONS_WORKER_RUNTIME": "python", "FUNCTIONS_EXTENSION_VERSION": "~4", "ADO_ORGANIZATION": "virginatlantic", "ADO_PROJECT": "Air4 Channels Testing", "ADO_PAT_TOKEN": "Fmx8ttZ3CgzUXnutPp4q3DrlUwhNX01vgYP4vmDrNjstas7TuxgXJQQJ99BIACAAAAAFgb9wAAASAZDO4Ku8", "AZURE_SEARCH_SERVICE_NAME": "your-search-service", "AZURE_SEARCH_ADMIN_KEY": "your-search-key", "AZURE_SEARCH_INDEX_NAME": "workitems", "KEY_VAULT_URL": "https://your-keyvault.vault.azure.net/", "TEAMS_WEBHOOK_URL": "https://outlook.office.com/webhook/...", "EMBEDDING_PROVIDER": "sentence_transformers", "EMBEDDING_MODEL": "microsoft/E5-large-v2", "OPENAI_API_KEY": "********************************************************************************************************************************************************************", "AZURE_OPENAI_ENDPOINT": "https://your-openai.openai.azure.com/", "AZURE_OPENAI_API_KEY": "your-azure-openai-key", "AZURE_OPENAI_API_VERSION": "2024-02-01", "LOG_LEVEL": "INFO", "ENVIRONMENT": "development"}, "Host": {"LocalHttpPort": 7071, "CORS": "*", "CORSCredentials": false}}