#!/usr/bin/env python3
"""
Test script to verify all dependencies are installed correctly.
"""

import sys
import importlib

def test_import(module_name, description=""):
    """Test if a module can be imported."""
    try:
        importlib.import_module(module_name)
        print(f"✅ {module_name} - {description}")
        return True
    except ImportError as e:
        print(f"❌ {module_name} - {description} - Error: {e}")
        return False

def main():
    """Test all required dependencies."""
    print("🔍 Testing QA AI Triage Dependencies")
    print("=" * 50)
    
    success_count = 0
    total_count = 0
    
    # Core dependencies
    dependencies = [
        ("azure.identity", "Azure Identity for authentication"),
        ("azure.keyvault.secrets", "Azure Key Vault for secrets"),
        ("azure.search.documents", "Azure AI Search"),
        ("azure.storage.blob", "Azure Blob Storage"),
        ("pydantic", "Data validation and serialization"),
        ("pandas", "Data processing"),
        ("numpy", "Numerical computing"),
        ("openai", "OpenAI API client"),
        ("sentence_transformers", "Sentence embeddings"),
        ("torch", "PyTorch for ML"),
        ("sklearn", "Scikit-learn for ML"),
        ("nltk", "Natural language processing"),
        ("spacy", "Advanced NLP"),
        ("textdistance", "Text similarity"),
        ("aiohttp", "Async HTTP client"),
        ("requests", "HTTP client"),
        ("httpx", "Modern HTTP client"),
        ("asyncio_throttle", "Async throttling"),
        ("structlog", "Structured logging"),
        ("pythonjsonlogger", "JSON logging"),
        ("pytest", "Testing framework"),
        ("pytest_asyncio", "Async testing"),
        ("pytest_mock", "Mocking for tests"),
        ("pytest_cov", "Test coverage"),
        ("black", "Code formatting"),
        ("flake8", "Code linting"),
        ("mypy", "Type checking"),
        ("jupyter", "Jupyter notebooks"),
        ("matplotlib", "Plotting"),
        ("seaborn", "Statistical plotting"),
        ("ipywidgets", "Interactive widgets"),
        ("click", "CLI framework"),
        ("tenacity", "Retry library"),
        ("dotenv", "Environment variables"),
    ]
    
    for module, description in dependencies:
        if test_import(module, description):
            success_count += 1
        total_count += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Results: {success_count}/{total_count} dependencies installed successfully")
    
    if success_count == total_count:
        print("🎉 All dependencies are installed and working!")
        return 0
    else:
        print(f"⚠️  {total_count - success_count} dependencies failed to import")
        return 1

if __name__ == "__main__":
    sys.exit(main())
