"""
Test script for the workitem_created Azure Function.
This script helps you test the function locally with sample data.
"""

import json
import asyncio
import sys
import os
from typing import Dict, Any

# Add the __app__ directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '__app__'))

# Mock Azure Functions HttpRequest for testing
class MockHttpRequest:
    def __init__(self, method: str = "POST", url: str = "http://localhost:7071/api/workitem_created", 
                 headers: Dict[str, str] = None, body: str = ""):
        self.method = method
        self.url = url
        self.headers = headers or {"Content-Type": "application/json"}
        self._body = body
    
    def get_json(self):
        if self._body:
            return json.loads(self._body)
        return None


def create_sample_ado_webhook_payload() -> Dict[str, Any]:
    """
    Create a sample Azure DevOps webhook payload for testing.
    This mimics what ADO sends when a work item is created.
    """
    return {
        "subscriptionId": "********-1234-1234-1234-************",
        "notificationId": 1,
        "id": "********-1234-1234-1234-************",
        "eventType": "workitem.created",
        "publisherId": "tfs",
        "message": {
            "text": "Bug #123 (Sample Bug) created by John Doe"
        },
        "detailedMessage": {
            "text": "Bug #123 (Sample Bug) created by John Doe"
        },
        "resource": {
            "id": 123,
            "workItemId": 123,
            "rev": 1,
            "revisedBy": {
                "displayName": "John Doe",
                "id": "********-1234-1234-1234-************"
            },
            "revisedDate": "2024-01-15T10:30:00.000Z",
            "fields": {
                "System.Id": 123,
                "System.Title": "Application crashes when clicking the submit button",
                "System.Description": "When users click the submit button on the contact form, the application crashes with a null reference exception. This happens consistently across different browsers.",
                "System.WorkItemType": "Bug",
                "System.State": "New",
                "System.AreaPath": "MyProject\\Frontend\\Forms",
                "System.IterationPath": "MyProject\\Sprint 1",
                "System.AssignedTo": "",
                "System.CreatedBy": {
                    "displayName": "John Doe",
                    "id": "********-1234-1234-1234-************"
                },
                "System.CreatedDate": "2024-01-15T10:30:00.000Z",
                "System.ChangedDate": "2024-01-15T10:30:00.000Z",
                "Microsoft.VSTS.Common.Priority": 2,
                "Microsoft.VSTS.Common.Severity": "2 - High",
                "System.Tags": "frontend; forms; crash",
                "Microsoft.VSTS.TCM.ReproSteps": "1. Navigate to contact form\n2. Fill in all required fields\n3. Click submit button\n4. Application crashes",
                "Microsoft.VSTS.TCM.SystemInfo": "Windows 10, Chrome 120.0.6099.109"
            }
        },
        "resourceVersion": "1.0",
        "resourceContainers": {
            "collection": {
                "id": "********-1234-1234-1234-************"
            },
            "account": {
                "id": "********-1234-1234-1234-************"
            },
            "project": {
                "id": "********-1234-1234-1234-************"
            }
        },
        "createdDate": "2024-01-15T10:30:00.000Z"
    }


async def test_workitem_function():
    """
    Test the workitem_created function with sample data.
    """
    try:
        # Import the function
        from workitem_created import main as workitem_created_main
        
        # Create sample webhook payload
        webhook_payload = create_sample_ado_webhook_payload()
        
        # Create mock HTTP request
        mock_request = MockHttpRequest(
            method="POST",
            body=json.dumps(webhook_payload)
        )
        
        print("🚀 Testing workitem_created function...")
        print(f"📝 Sample work item: {webhook_payload['resource']['fields']['System.Title']}")
        print(f"🔧 Work item type: {webhook_payload['resource']['fields']['System.WorkItemType']}")
        print(f"⚡ Priority: {webhook_payload['resource']['fields']['Microsoft.VSTS.Common.Priority']}")
        print()
        
        # Call the function
        response = await workitem_created_main(mock_request)
        
        print("✅ Function executed successfully!")
        print(f"📊 Status Code: {response.status_code}")
        print(f"📄 Response: {response.get_body().decode()}")
        
    except Exception as e:
        print(f"❌ Error testing function: {e}")
        import traceback
        traceback.print_exc()


def test_webhook_payload_extraction():
    """
    Test the webhook payload extraction logic.
    """
    try:
        from workitem_created import extract_work_item_from_webhook
        
        webhook_payload = create_sample_ado_webhook_payload()
        work_item_data = extract_work_item_from_webhook(webhook_payload)
        
        print("🔍 Testing webhook payload extraction...")
        print(f"📝 Extracted work item data:")
        for key, value in work_item_data.items():
            print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"❌ Error testing payload extraction: {e}")
        import traceback
        traceback.print_exc()


async def test_simple_extraction():
    """
    Simple test of just the extraction logic without full function.
    """
    try:
        from workitem_created import extract_work_item_from_webhook

        webhook_payload = create_sample_ado_webhook_payload()
        work_item_data = extract_work_item_from_webhook(webhook_payload)

        print("✅ Webhook extraction test passed!")
        print(f"📝 Work Item ID: {work_item_data['id']}")
        print(f"📋 Title: {work_item_data['title']}")
        print(f"🐛 Type: {work_item_data['work_item_type']}")
        print(f"⚡ Priority: {work_item_data['priority']}")

        return True

    except Exception as e:
        print(f"❌ Extraction test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🧪 AutoDefectTriage Function Test Suite")
    print("=" * 50)

    # Test payload extraction first
    print("🔍 Testing webhook payload extraction...")
    if test_webhook_payload_extraction():
        print()

        # Test simple extraction
        print("🔧 Testing simple extraction...")
        asyncio.run(test_simple_extraction())
        print()

        # Try the full function test (may fail due to missing dependencies)
        print("🚀 Testing full function (may fail due to missing Azure services)...")
        try:
            asyncio.run(test_workitem_function())
        except Exception as e:
            print(f"⚠️  Full function test failed (expected): {e}")
            print("💡 This is normal if Azure services are not configured.")
    else:
        print("❌ Basic tests failed, skipping advanced tests.")
