#!/usr/bin/env python3
"""
Simple Azure DevOps connection test using direct HTTP requests.
"""

import asyncio
import httpx
import base64
import json
from urllib.parse import quote

# Your configuration
ADO_ORGANIZATION = "virginatlantic"
ADO_PROJECT = "Air4 Channels Testing"
ADO_PAT_TOKEN = "Fmx8ttZ3CgzUXnutPp4q3DrlUwhNX01vgYP4vmDrNjstas7TuxgXJQQJ99BIACAAAAAFgb9wAAASAZDO4Ku8"

def encode_pat_token(pat_token: str) -> str:
    """Encode PAT token for basic auth."""
    token_bytes = f":{pat_token}".encode('ascii')
    return base64.b64encode(token_bytes).decode('ascii')

async def test_basic_connection():
    """Test basic connection to Azure DevOps."""
    print("🔍 Simple Azure DevOps Connection Test")
    print("=" * 50)
    
    # Encode project name for URL
    encoded_project = quote(ADO_PROJECT)
    base_url = f"https://dev.azure.com/{ADO_ORGANIZATION}"
    
    print(f"Organization: {ADO_ORGANIZATION}")
    print(f"Project: {ADO_PROJECT}")
    print(f"Encoded Project: {encoded_project}")
    print(f"Base URL: {base_url}")
    print(f"PAT Token: {'*' * (len(ADO_PAT_TOKEN) - 4) + ADO_PAT_TOKEN[-4:]}")
    
    # Create HTTP client
    auth_header = f"Basic {encode_pat_token(ADO_PAT_TOKEN)}"
    headers = {
        "Authorization": auth_header,
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        
        # Test 1: Get organization info
        print("\n1. Testing organization access...")
        try:
            org_url = f"https://dev.azure.com/{ADO_ORGANIZATION}/_apis/projects"
            response = await client.get(org_url, headers=headers, params={"api-version": "7.0"})
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                projects = response.json()
                project_names = [p['name'] for p in projects.get('value', [])]
                print(f"   ✅ Found {len(project_names)} projects")
                print(f"   Projects: {', '.join(project_names)}")
                
                # Check if our project exists
                if ADO_PROJECT in project_names:
                    print(f"   ✅ Target project '{ADO_PROJECT}' found!")
                else:
                    print(f"   ⚠️  Target project '{ADO_PROJECT}' not found in accessible projects")
                    
            elif response.status_code == 401:
                print("   ❌ Authentication failed - PAT token may be invalid")
                return False
            elif response.status_code == 403:
                print("   ❌ Access denied - PAT token may not have sufficient permissions")
                return False
            else:
                print(f"   ❌ Unexpected status code: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            return False
        
        # Test 2: Get specific project info
        print("\n2. Testing specific project access...")
        try:
            project_url = f"https://dev.azure.com/{ADO_ORGANIZATION}/_apis/projects/{encoded_project}"
            response = await client.get(project_url, headers=headers, params={"api-version": "7.0"})
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                project_info = response.json()
                print(f"   ✅ Project access successful!")
                print(f"   Project ID: {project_info.get('id', 'Unknown')}")
                print(f"   Project Name: {project_info.get('name', 'Unknown')}")
                print(f"   Project State: {project_info.get('state', 'Unknown')}")
            else:
                print(f"   ❌ Failed to access project: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Test 3: Simple work item query
        print("\n3. Testing work item query...")
        try:
            wiql_url = f"https://dev.azure.com/{ADO_ORGANIZATION}/{encoded_project}/_apis/wit/wiql"
            simple_query = {
                "query": "SELECT [System.Id] FROM WorkItems WHERE [System.TeamProject] = @project"
            }
            
            response = await client.post(
                wiql_url, 
                headers=headers, 
                params={"api-version": "7.0"},
                json=simple_query
            )
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                work_items = result.get('workItems', [])
                print(f"   ✅ Query successful! Found {len(work_items)} work items")
            elif response.status_code == 400:
                print(f"   ❌ Bad request - Query syntax may be invalid")
                print(f"   Response: {response.text}")
            else:
                print(f"   ❌ Query failed: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Test 4: Get work item types
        print("\n4. Testing work item types...")
        try:
            wit_url = f"https://dev.azure.com/{ADO_ORGANIZATION}/{encoded_project}/_apis/wit/workitemtypes"
            response = await client.get(wit_url, headers=headers, params={"api-version": "7.0"})
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                wit_result = response.json()
                work_item_types = [wit['name'] for wit in wit_result.get('value', [])]
                print(f"   ✅ Available work item types: {', '.join(work_item_types)}")
            else:
                print(f"   ❌ Failed to get work item types: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n🎯 Connection test completed!")
    return True

def main():
    """Main test function."""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        success = loop.run_until_complete(test_basic_connection())
        return 0 if success else 1
    except Exception as e:
        print(f"💥 Test execution failed: {e}")
        return 1
    finally:
        loop.close()

if __name__ == "__main__":
    import sys
    sys.exit(main())
