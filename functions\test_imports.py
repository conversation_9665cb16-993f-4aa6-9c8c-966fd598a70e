#!/usr/bin/env python3
"""
Test script to verify that all imports are working correctly.
This tests the fix for the "No module named '__app__.common'" error.
"""

import sys
import traceback

def test_import(module_name, description):
    """Test importing a module and report the result."""
    try:
        exec(f"import {module_name}")
        print(f"✅ {description}: SUCCESS")
        return True
    except Exception as e:
        print(f"❌ {description}: FAILED - {e}")
        return False

def test_from_import(import_statement, description):
    """Test a from...import statement and report the result."""
    try:
        exec(import_statement)
        print(f"✅ {description}: SUCCESS")
        return True
    except Exception as e:
        print(f"❌ {description}: FAILED - {e}")
        return False

def main():
    """Run all import tests."""
    print("🧪 Testing AutoDefectTriage Function Imports")
    print("=" * 60)
    print(f"Python version: {sys.version}")
    print(f"Python path includes: {sys.path[0]}")
    print()
    
    tests_passed = 0
    total_tests = 0
    
    # Test basic package imports
    tests = [
        ("__app__", "Main app package"),
        ("__app__.common", "Common package"),
        ("__app__.common.models", "Models package"),
        ("__app__.common.adapters", "Adapters package"),
        ("__app__.common.ai", "AI package"),
        ("__app__.common.utils", "Utils package"),
    ]
    
    for module, description in tests:
        total_tests += 1
        if test_import(module, description):
            tests_passed += 1
    
    print()
    print("🔍 Testing specific imports...")
    
    # Test specific class imports
    specific_imports = [
        ("from __app__.common.models.schemas import WorkItem", "WorkItem model"),
        ("from __app__.common.models.schemas import TriageResult", "TriageResult model"),
        ("from __app__.common.adapters.ado_client import AdoClient", "ADO Client"),
        ("from __app__.common.adapters.search_client import SearchClient", "Search Client"),
        ("from __app__.common.adapters.teams_client import TeamsClient", "Teams Client"),
        ("from __app__.common.ai.duplicate import DuplicateDetector", "Duplicate Detector"),
        ("from __app__.common.ai.assigner import AssignmentEngine", "Assignment Engine"),
        ("from __app__.common.ai.priority import PriorityEngine", "Priority Engine"),
        ("from __app__.common.utils.config import get_config", "Config utility"),
        ("from __app__.common.utils.logging import setup_logging", "Logging utility"),
    ]
    
    for import_stmt, description in specific_imports:
        total_tests += 1
        if test_from_import(import_stmt, description):
            tests_passed += 1
    
    print()
    print("🚀 Testing function imports...")
    
    # Test function imports
    function_imports = [
        ("from __app__.workitem_created.handler import process_workitem_webhook", "WorkItem webhook handler"),
        ("from __app__.backfill_job import main", "Backfill job main function"),
    ]
    
    for import_stmt, description in function_imports:
        total_tests += 1
        if test_from_import(import_stmt, description):
            tests_passed += 1
    
    print()
    print("📊 Test Results")
    print("=" * 30)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 All imports working correctly!")
        print("✅ The '__app__.common' module error should be resolved.")
        return True
    else:
        print("⚠️  Some imports failed. Check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
