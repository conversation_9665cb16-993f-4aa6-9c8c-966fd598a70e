"""
Tests for assignment engine functionality.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

from __app__.common.models.schemas import WorkItem, SearchResult
from __app__.common.ai.assigner import Assignment<PERSON><PERSON><PERSON>, AssignmentCandidate, TeamMemberLoad
from __app__.common.adapters.search_client import Search<PERSON><PERSON>
from __app__.common.utils.config import Config


@pytest.fixture
def mock_config():
    """Mock configuration for testing."""
    config = Mock(spec=Config)
    config.get.side_effect = lambda key, default=None: {
        'ASSIGNMENT_KNN_K': 10,
        'ASSIGNMENT_MIN_CONFIDENCE': 0.6,
        'ASSIGNMENT_LOAD_WEIGHT': 0.3,
        'ASSIGNMENT_OWNERSHIP_WEIGHT': 0.4,
        'ASSIGNMENT_SIMILARITY_WEIGHT': 0.3
    }.get(key, default)
    return config


@pytest.fixture
def mock_search_client():
    """Mock search client for testing."""
    return Mock(spec=SearchClient)


@pytest.fixture
def sample_work_item():
    """Sample work item for testing."""
    return WorkItem(
        id=12345,
        title="API endpoint returns 500 error",
        description="The /api/users endpoint is returning 500 internal server error",
        work_item_type="Bug",
        state="New",
        area_path="MyProject\\API\\Users",
        assigned_to="",
        created_by="<EMAIL>",
        created_date="2024-01-15T10:00:00Z",
        changed_date="2024-01-15T10:00:00Z",
        priority=2,
        tags="api, backend, error",
        repro_steps="1. Call GET /api/users\n2. Observe 500 error",
        system_info="Production environment"
    )


@pytest.fixture
def sample_similar_items():
    """Sample similar work items for kNN testing."""
    return [
        SearchResult(
            work_item_id=11111,
            title="API authentication error",
            description="Authentication endpoint returning errors",
            work_item_type="Bug",
            area_path="MyProject\\API\\Auth",
            score=8.5
        ),
        SearchResult(
            work_item_id=22222,
            title="Database connection timeout in API",
            description="API calls timing out due to database issues",
            work_item_type="Bug",
            area_path="MyProject\\API\\Database",
            score=7.8
        ),
        SearchResult(
            work_item_id=33333,
            title="User service performance issue",
            description="User service responding slowly",
            work_item_type="Bug",
            area_path="MyProject\\API\\Users",
            score=7.2
        )
    ]


class TestAssignmentEngine:
    """Test cases for AssignmentEngine class."""
    
    def test_init(self, mock_search_client, mock_config):
        """Test AssignmentEngine initialization."""
        engine = AssignmentEngine(mock_search_client, mock_config)
        
        assert engine.search_client == mock_search_client
        assert engine.config == mock_config
        assert engine.knn_k == 10
        assert engine.min_confidence == 0.6
    
    @pytest.mark.asyncio
    async def test_assign_work_item_no_candidates(self, mock_search_client, mock_config, sample_work_item):
        """Test assignment when no candidates are found."""
        engine = AssignmentEngine(mock_search_client, mock_config)
        
        # Mock all candidate generation methods to return empty lists
        with patch.object(engine, '_get_assignment_candidates', return_value=[]):
            
            assignee = await engine.assign_work_item(sample_work_item)
            
            assert assignee is None
    
    @pytest.mark.asyncio
    async def test_assign_work_item_low_confidence(self, mock_search_client, mock_config, sample_work_item):
        """Test assignment when confidence is too low."""
        engine = AssignmentEngine(mock_search_client, mock_config)
        
        # Create a low-confidence candidate
        low_confidence_candidate = AssignmentCandidate(
            assignee="<EMAIL>",
            confidence_score=0.3,  # Below threshold
            reasoning=["Low confidence match"],
            vote_count=1,
            ownership_match=False,
            load_penalty=0.0
        )
        
        with patch.object(engine, '_get_assignment_candidates', return_value=[low_confidence_candidate]):
            
            assignee = await engine.assign_work_item(sample_work_item)
            
            assert assignee is None
    
    @pytest.mark.asyncio
    async def test_assign_work_item_success(self, mock_search_client, mock_config, sample_work_item):
        """Test successful assignment."""
        engine = AssignmentEngine(mock_search_client, mock_config)
        
        # Create a high-confidence candidate
        high_confidence_candidate = AssignmentCandidate(
            assignee="<EMAIL>",
            confidence_score=0.8,  # Above threshold
            reasoning=["Strong kNN match", "Code ownership"],
            vote_count=5,
            ownership_match=True,
            load_penalty=0.2
        )
        
        with patch.object(engine, '_get_assignment_candidates', return_value=[high_confidence_candidate]):
            
            assignee = await engine.assign_work_item(sample_work_item)
            
            assert assignee == "<EMAIL>"
    
    @pytest.mark.asyncio
    async def test_get_knn_candidates(self, mock_search_client, mock_config, sample_work_item, sample_similar_items):
        """Test kNN candidate generation."""
        engine = AssignmentEngine(mock_search_client, mock_config)
        
        # Add assigned_to attribute to search results for testing
        for item in sample_similar_items:
            setattr(item, 'assigned_to', f"dev{item.work_item_id}@company.com")
        
        # Mock embedding service
        with patch.object(engine, 'embedding_service') as mock_embedding:
            mock_embedding.embed_work_item.return_value = [0.1] * 384
            
            # Mock search client
            mock_search_client.hybrid_search = AsyncMock(return_value=sample_similar_items)
            
            candidates = await engine._get_knn_candidates(sample_work_item)
            
            assert len(candidates) > 0
            assert all(isinstance(c, AssignmentCandidate) for c in candidates)
            assert all(c.vote_count > 0 for c in candidates)
    
    @pytest.mark.asyncio
    async def test_get_ownership_candidates(self, mock_search_client, mock_config, sample_work_item):
        """Test ownership-based candidate generation."""
        engine = AssignmentEngine(mock_search_client, mock_config)
        
        # Mock codeowners parser
        with patch.object(engine, 'codeowners_parser') as mock_parser:
            mock_parser.get_owners_for_area = AsyncMock(return_value=["<EMAIL>", "<EMAIL>"])
            
            candidates = await engine._get_ownership_candidates(sample_work_item)
            
            assert len(candidates) == 2
            assert all(c.ownership_match for c in candidates)
            assert "<EMAIL>" in [c.assignee for c in candidates]
            assert "<EMAIL>" in [c.assignee for c in candidates]
    
    @pytest.mark.asyncio
    async def test_get_heuristic_candidates(self, mock_search_client, mock_config, sample_work_item):
        """Test heuristic-based candidate generation."""
        engine = AssignmentEngine(mock_search_client, mock_config)
        
        # Mock ownership heuristics
        with patch.object(engine, 'ownership_heuristics') as mock_heuristics:
            mock_heuristics.suggest_assignees = AsyncMock(return_value=[
                {"assignee": "<EMAIL>", "reason": "API keyword match"}
            ])
            
            candidates = await engine._get_heuristic_candidates(sample_work_item)
            
            assert len(candidates) == 1
            assert candidates[0].assignee == "<EMAIL>"
            assert "API keyword match" in candidates[0].reasoning
    
    @pytest.mark.asyncio
    async def test_apply_load_balancing(self, mock_search_client, mock_config):
        """Test load balancing application."""
        engine = AssignmentEngine(mock_search_client, mock_config)
        
        candidates = [
            AssignmentCandidate(
                assignee="<EMAIL>",
                confidence_score=0.0,
                reasoning=[],
                vote_count=0,
                ownership_match=False,
                load_penalty=0.0
            ),
            AssignmentCandidate(
                assignee="<EMAIL>",
                confidence_score=0.0,
                reasoning=[],
                vote_count=0,
                ownership_match=False,
                load_penalty=0.0
            )
        ]
        
        # Mock team member loads
        mock_loads = {
            "<EMAIL>": TeamMemberLoad(
                assignee="<EMAIL>",
                active_items=10,
                recent_assignments=5,
                avg_resolution_time=0.0,
                expertise_areas=[]
            ),
            "<EMAIL>": TeamMemberLoad(
                assignee="<EMAIL>",
                active_items=2,
                recent_assignments=1,
                avg_resolution_time=0.0,
                expertise_areas=[]
            )
        }
        
        with patch.object(engine, '_get_team_member_loads', return_value=mock_loads):
            await engine._apply_load_balancing(candidates)
            
            # Heavy load should have higher penalty
            heavy_candidate = next(c for c in candidates if c.assignee == "<EMAIL>")
            light_candidate = next(c for c in candidates if c.assignee == "<EMAIL>")
            
            assert heavy_candidate.load_penalty > light_candidate.load_penalty
    
    def test_calculate_confidence_score(self, mock_search_client, mock_config):
        """Test confidence score calculation."""
        engine = AssignmentEngine(mock_search_client, mock_config)
        
        # Test high-confidence candidate
        high_candidate = AssignmentCandidate(
            assignee="<EMAIL>",
            confidence_score=0.0,  # Will be calculated
            reasoning=[],
            vote_count=8,  # High vote count
            ownership_match=True,  # Has ownership
            load_penalty=0.1  # Low load
        )
        
        confidence = engine._calculate_confidence_score(high_candidate)
        
        assert 0.0 <= confidence <= 1.0
        assert confidence > 0.7  # Should be high
        
        # Test low-confidence candidate
        low_candidate = AssignmentCandidate(
            assignee="<EMAIL>",
            confidence_score=0.0,
            reasoning=[],
            vote_count=1,  # Low vote count
            ownership_match=False,  # No ownership
            load_penalty=0.9  # High load
        )
        
        low_confidence = engine._calculate_confidence_score(low_candidate)
        
        assert low_confidence < confidence  # Should be lower
    
    def test_select_best_candidate(self, mock_search_client, mock_config):
        """Test best candidate selection."""
        engine = AssignmentEngine(mock_search_client, mock_config)
        
        candidates = [
            AssignmentCandidate(
                assignee="<EMAIL>",
                confidence_score=0.6,
                reasoning=[],
                vote_count=0,
                ownership_match=False,
                load_penalty=0.0
            ),
            AssignmentCandidate(
                assignee="<EMAIL>",
                confidence_score=0.9,  # Highest confidence
                reasoning=[],
                vote_count=0,
                ownership_match=False,
                load_penalty=0.0
            ),
            AssignmentCandidate(
                assignee="<EMAIL>",
                confidence_score=0.3,
                reasoning=[],
                vote_count=0,
                ownership_match=False,
                load_penalty=0.0
            )
        ]
        
        best = engine._select_best_candidate(candidates)
        
        assert best.assignee == "<EMAIL>"
        assert best.confidence_score == 0.9


@pytest.mark.asyncio
async def test_integration_assignment_workflow(mock_config):
    """Integration test for assignment workflow."""
    # Create a realistic work item
    work_item = WorkItem(
        id=99999,
        title="User authentication API failing",
        description="The user authentication API is returning 401 errors",
        work_item_type="Bug",
        state="New",
        area_path="MyProject\\API\\Authentication",
        priority=1
    )
    
    # Mock search client with realistic responses
    mock_search_client = Mock(spec=SearchClient)
    
    # Mock similar items with assignees
    similar_items = [
        SearchResult(work_item_id=1, title="Auth API issue", description="", work_item_type="Bug", area_path="", score=8.0),
        SearchResult(work_item_id=2, title="Login API error", description="", work_item_type="Bug", area_path="", score=7.5)
    ]
    
    # Add assignee attributes
    setattr(similar_items[0], 'assigned_to', '<EMAIL>')
    setattr(similar_items[1], 'assigned_to', '<EMAIL>')
    
    mock_search_client.hybrid_search = AsyncMock(return_value=similar_items)
    
    engine = AssignmentEngine(mock_search_client, mock_config)
    
    # Mock dependencies
    with patch.object(engine, 'embedding_service') as mock_embedding:
        mock_embedding.embed_work_item.return_value = [0.1] * 384
        
        with patch.object(engine, 'codeowners_parser') as mock_parser:
            mock_parser.get_owners_for_area = AsyncMock(return_value=['<EMAIL>'])
            
            with patch.object(engine, 'ownership_heuristics') as mock_heuristics:
                mock_heuristics.suggest_assignees = AsyncMock(return_value=[])
                
                with patch.object(engine, '_get_team_member_loads', return_value={}):
                    
                    assignee = await engine.assign_work_item(work_item)
                    
                    assert assignee == '<EMAIL>'


if __name__ == "__main__":
    pytest.main([__file__])
