"""
CODEOWNERS parser for resolving area path to code owners.
"""

import logging
import re
from typing import List, Dict, Optional, Set
from pathlib import Path
import fnmatch

from ..utils.config import Config
from ..utils.logging import log_structured

logger = logging.getLogger(__name__)


class CodeOwnersParser:
    """Parser for CODEOWNERS files to determine code ownership."""
    
    def __init__(self, config: Config):
        self.config = config
        self.codeowners_rules: List[Dict[str, any]] = []
        self.area_path_mappings: Dict[str, List[str]] = {}
        self._load_codeowners()
        self._load_area_mappings()
    
    def _load_codeowners(self) -> None:
        """Load CODEOWNERS file and parse rules."""
        try:
            codeowners_path = self.config.get('CODEOWNERS_PATH', '.github/CODEOWNERS')
            
            # Try to load from file system (for local development)
            if Path(codeowners_path).exists():
                with open(codeowners_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                self._parse_codeowners_content(content)
            else:
                # Load default rules if no file found
                self._load_default_rules()
                
            log_structured(
                logger,
                "info",
                "CODEOWNERS rules loaded",
                extra={"rule_count": len(self.codeowners_rules)}
            )
            
        except Exception as e:
            logger.warning(f"Failed to load CODEOWNERS file: {e}")
            self._load_default_rules()
    
    def _parse_codeowners_content(self, content: str) -> None:
        """Parse CODEOWNERS file content."""
        self.codeowners_rules = []
        
        for line_num, line in enumerate(content.splitlines(), 1):
            line = line.strip()
            
            # Skip empty lines and comments
            if not line or line.startswith('#'):
                continue
            
            try:
                rule = self._parse_codeowners_line(line)
                if rule:
                    rule['line_number'] = line_num
                    self.codeowners_rules.append(rule)
            except Exception as e:
                logger.warning(f"Error parsing CODEOWNERS line {line_num}: {e}")
    
    def _parse_codeowners_line(self, line: str) -> Optional[Dict[str, any]]:
        """Parse a single CODEOWNERS line."""
        # Split line into path pattern and owners
        parts = line.split()
        if len(parts) < 2:
            return None
        
        path_pattern = parts[0]
        owners = []
        
        # Parse owners (can be @username, @team/name, or email)
        for owner in parts[1:]:
            if owner.startswith('@'):
                # GitHub username or team
                owners.append(owner[1:])  # Remove @ prefix
            elif '@' in owner:
                # Email address
                owners.append(owner)
            else:
                # Plain username
                owners.append(owner)
        
        return {
            'pattern': path_pattern,
            'owners': owners,
            'regex': self._convert_pattern_to_regex(path_pattern)
        }
    
    def _convert_pattern_to_regex(self, pattern: str) -> str:
        """Convert CODEOWNERS pattern to regex."""
        # Handle special CODEOWNERS patterns
        if pattern.startswith('/'):
            # Absolute path from root
            pattern = pattern[1:]
        
        if pattern.endswith('/'):
            # Directory pattern
            pattern = pattern + '**'
        
        # Convert glob pattern to regex
        regex_pattern = fnmatch.translate(pattern)
        return regex_pattern
    
    def _load_default_rules(self) -> None:
        """Load default ownership rules when no CODEOWNERS file is found."""
        default_rules = [
            {
                'pattern': 'src/authentication/**',
                'owners': ['security-team', 'auth-team'],
                'regex': fnmatch.translate('src/authentication/**')
            },
            {
                'pattern': 'src/payment/**',
                'owners': ['payment-team', 'finance-team'],
                'regex': fnmatch.translate('src/payment/**')
            },
            {
                'pattern': 'src/api/**',
                'owners': ['backend-team', 'api-team'],
                'regex': fnmatch.translate('src/api/**')
            },
            {
                'pattern': 'src/ui/**',
                'owners': ['frontend-team', 'ui-team'],
                'regex': fnmatch.translate('src/ui/**')
            },
            {
                'pattern': 'docs/**',
                'owners': ['docs-team', 'technical-writers'],
                'regex': fnmatch.translate('docs/**')
            },
            {
                'pattern': '*.md',
                'owners': ['docs-team'],
                'regex': fnmatch.translate('*.md')
            }
        ]
        
        self.codeowners_rules = default_rules
        logger.info("Loaded default CODEOWNERS rules")
    
    def _load_area_mappings(self) -> None:
        """Load mappings from Azure DevOps area paths to file system paths."""
        # This would typically be loaded from configuration or a mapping file
        # For now, we'll use some common mappings
        self.area_path_mappings = {
            'MyProject\\Authentication': ['src/authentication/', 'auth/'],
            'MyProject\\Payment': ['src/payment/', 'billing/'],
            'MyProject\\API': ['src/api/', 'api/', 'services/'],
            'MyProject\\UI': ['src/ui/', 'frontend/', 'web/'],
            'MyProject\\Database': ['src/data/', 'database/', 'migrations/'],
            'MyProject\\Security': ['src/security/', 'auth/', 'src/authentication/'],
            'MyProject\\Documentation': ['docs/', 'documentation/'],
            'MyProject\\Testing': ['tests/', 'test/'],
            'MyProject\\Infrastructure': ['infra/', 'infrastructure/', 'deploy/']
        }
        
        # Load custom mappings from config if available
        custom_mappings = self.config.get('AREA_PATH_MAPPINGS', {})
        if custom_mappings:
            self.area_path_mappings.update(custom_mappings)
    
    async def get_owners_for_area(self, area_path: Optional[str]) -> List[str]:
        """
        Get code owners for a given area path.
        
        Args:
            area_path: Azure DevOps area path
        
        Returns:
            List of owner usernames/emails
        """
        if not area_path:
            return []
        
        try:
            # First, try direct area path mapping
            owners = self._get_owners_from_area_mapping(area_path)
            if owners:
                return owners
            
            # Then try to map area path to file paths and find owners
            file_paths = self._map_area_to_file_paths(area_path)
            for file_path in file_paths:
                owners = self._get_owners_for_path(file_path)
                if owners:
                    return owners
            
            # Fallback: try to extract component from area path
            component_owners = self._get_owners_from_component(area_path)
            if component_owners:
                return component_owners
            
            log_structured(
                logger,
                "debug",
                "No owners found for area path",
                extra={"area_path": area_path}
            )
            
            return []
            
        except Exception as e:
            logger.error(f"Error getting owners for area path {area_path}: {e}")
            return []
    
    def _get_owners_from_area_mapping(self, area_path: str) -> List[str]:
        """Get owners from direct area path mapping."""
        # Check for exact match first
        if area_path in self.area_path_mappings:
            file_paths = self.area_path_mappings[area_path]
            for file_path in file_paths:
                owners = self._get_owners_for_path(file_path)
                if owners:
                    return owners
        
        # Check for partial matches
        for mapped_area, file_paths in self.area_path_mappings.items():
            if area_path.startswith(mapped_area) or mapped_area in area_path:
                for file_path in file_paths:
                    owners = self._get_owners_for_path(file_path)
                    if owners:
                        return owners
        
        return []
    
    def _map_area_to_file_paths(self, area_path: str) -> List[str]:
        """Map area path to potential file system paths."""
        file_paths = []
        
        # Extract component name from area path
        # Example: "MyProject\\Authentication\\Login" -> "authentication", "login"
        parts = area_path.split('\\')
        if len(parts) > 1:
            # Skip the project name (first part)
            components = [part.lower() for part in parts[1:]]
            
            # Generate potential file paths
            for component in components:
                file_paths.extend([
                    f"src/{component}/",
                    f"{component}/",
                    f"src/{component}/**",
                    f"{component}/**"
                ])
        
        return file_paths
    
    def _get_owners_for_path(self, file_path: str) -> List[str]:
        """Get owners for a specific file path using CODEOWNERS rules."""
        owners = []
        
        # Check rules in reverse order (later rules override earlier ones)
        for rule in reversed(self.codeowners_rules):
            try:
                if re.match(rule['regex'], file_path, re.IGNORECASE):
                    owners.extend(rule['owners'])
                    break  # Use first matching rule (most specific)
            except re.error:
                # Skip invalid regex patterns
                continue
        
        return list(set(owners))  # Remove duplicates
    
    def _get_owners_from_component(self, area_path: str) -> List[str]:
        """Get owners based on component keywords in area path."""
        area_lower = area_path.lower()
        
        # Component-based ownership rules
        component_rules = {
            'authentication': ['security-team', 'auth-team'],
            'auth': ['security-team', 'auth-team'],
            'security': ['security-team'],
            'payment': ['payment-team', 'finance-team'],
            'billing': ['payment-team', 'finance-team'],
            'api': ['backend-team', 'api-team'],
            'service': ['backend-team'],
            'ui': ['frontend-team', 'ui-team'],
            'frontend': ['frontend-team'],
            'web': ['frontend-team'],
            'database': ['data-team', 'backend-team'],
            'data': ['data-team'],
            'infrastructure': ['devops-team', 'platform-team'],
            'deployment': ['devops-team'],
            'monitoring': ['devops-team', 'sre-team'],
            'documentation': ['docs-team'],
            'docs': ['docs-team'],
            'testing': ['qa-team', 'test-team'],
            'test': ['qa-team']
        }
        
        for component, owners in component_rules.items():
            if component in area_lower:
                return owners
        
        return []
    
    def get_all_owners(self) -> Set[str]:
        """Get all known owners from CODEOWNERS rules."""
        all_owners = set()
        
        for rule in self.codeowners_rules:
            all_owners.update(rule['owners'])
        
        return all_owners
    
    def validate_owner(self, owner: str) -> bool:
        """Validate if an owner exists in CODEOWNERS rules."""
        all_owners = self.get_all_owners()
        return owner in all_owners
