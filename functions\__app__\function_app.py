"""
Azure Functions App - AutoDefectTriage
Main function app configuration for Azure Functions v2 programming model.
"""

import azure.functions as func
import logging

# Create the function app
app = func.FunctionApp()

# Configure logging
logging.basicConfig(level=logging.INFO)


@app.function_name(name="workitem_created")
@app.route(route="workitem_created", auth_level=func.AuthLevel.FUNCTION)
async def workitem_created_trigger(req: func.HttpRequest) -> func.HttpResponse:
    """
    HTTP trigger for Azure DevOps Service Hook when work items are created/updated.

    This function receives webhooks from Azure DevOps when work items are created
    or updated, and processes them through the AI triage pipeline.
    """
    # Import the workitem processing logic
    from .workitem_created.handler import process_workitem_webhook
    return await process_workitem_webhook(req)


@app.function_name(name="backfill_job")
@app.timer_trigger(schedule="0 0 2 * * *", arg_name="timer", run_on_startup=False)
async def backfill_job_trigger(timer: func.TimerRequest) -> None:
    """
    Timer trigger for backfill job that runs daily at 2 AM.

    This function processes historical work items that may have been missed
    or need reprocessing through the triage pipeline.
    """
    # Import the backfill job logic
    from .backfill_job import main as backfill_job_main
    await backfill_job_main(timer)
