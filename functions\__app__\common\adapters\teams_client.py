"""
Microsoft Teams Client
Handles Teams notifications and Adaptive Cards for triage results.
"""

import json
import logging
from typing import Dict, List, Any, Optional
import httpx
from datetime import datetime

from ..models.schemas import WorkItem, TriageResult
from ..utils.config import Config
from ..utils.logging import log_structured
from ..cards.teams_adaptive import build_triage_card, build_duplicate_alert_card

logger = logging.getLogger(__name__)


class TeamsClient:
    """Client for Microsoft Teams webhook and Graph API operations."""
    
    def __init__(self, config: Config):
        self.config = config
        self.webhook_url = config.get('TEAMS_WEBHOOK_URL')
        self.graph_token = config.get('TEAMS_GRAPH_TOKEN')
        
        # Setup HTTP client
        self.client = httpx.AsyncClient(
            headers={
                "Content-Type": "application/json",
                "User-Agent": "QA-AI-Triage/1.0"
            },
            timeout=30.0
        )
    
    async def send_triage_notification(
        self, 
        work_item: WorkItem, 
        triage_result: TriageResult
    ) -> bool:
        """
        Send a triage notification to Teams channel.
        
        Args:
            work_item: The work item that was triaged
            triage_result: The triage results
        
        Returns:
            True if notification was sent successfully
        """
        try:
            if not self.webhook_url:
                logger.warning("Teams webhook URL not configured, skipping notification")
                return False
            
            # Build adaptive card for the notification
            card = build_triage_card(work_item, triage_result)
            
            # Send to Teams webhook
            response = await self.client.post(
                self.webhook_url,
                json=card
            )
            response.raise_for_status()
            
            log_structured(
                logger,
                "info",
                "Sent Teams triage notification",
                extra={
                    "work_item_id": work_item.id,
                    "assigned_to": triage_result.assigned_to,
                    "webhook_response_status": response.status_code
                }
            )
            
            return True
            
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error sending Teams notification: {e}")
            return False
        except Exception as e:
            logger.error(f"Error sending Teams notification: {e}")
            return False
    
    async def send_duplicate_alert(
        self, 
        work_item: WorkItem, 
        duplicate_items: List[Dict[str, Any]]
    ) -> bool:
        """
        Send a duplicate detection alert to Teams.
        
        Args:
            work_item: The new work item
            duplicate_items: List of potential duplicate work items
        
        Returns:
            True if alert was sent successfully
        """
        try:
            if not self.webhook_url:
                logger.warning("Teams webhook URL not configured, skipping duplicate alert")
                return False
            
            # Build adaptive card for duplicate alert
            card = build_duplicate_alert_card(work_item, duplicate_items)
            
            # Send to Teams webhook
            response = await self.client.post(
                self.webhook_url,
                json=card
            )
            response.raise_for_status()
            
            log_structured(
                logger,
                "info",
                "Sent Teams duplicate alert",
                extra={
                    "work_item_id": work_item.id,
                    "duplicate_count": len(duplicate_items),
                    "webhook_response_status": response.status_code
                }
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending Teams duplicate alert: {e}")
            return False
    
    async def send_custom_message(
        self, 
        title: str, 
        message: str, 
        color: str = "good"
    ) -> bool:
        """
        Send a custom message to Teams channel.
        
        Args:
            title: Message title
            message: Message content
            color: Message color (good, warning, attention)
        
        Returns:
            True if message was sent successfully
        """
        try:
            if not self.webhook_url:
                logger.warning("Teams webhook URL not configured, skipping custom message")
                return False
            
            # Build simple message card
            card = {
                "type": "message",
                "attachments": [
                    {
                        "contentType": "application/vnd.microsoft.card.adaptive",
                        "content": {
                            "type": "AdaptiveCard",
                            "version": "1.4",
                            "body": [
                                {
                                    "type": "TextBlock",
                                    "text": title,
                                    "weight": "Bolder",
                                    "size": "Medium",
                                    "color": self._get_color_for_theme(color)
                                },
                                {
                                    "type": "TextBlock",
                                    "text": message,
                                    "wrap": True
                                }
                            ]
                        }
                    }
                ]
            }
            
            # Send to Teams webhook
            response = await self.client.post(
                self.webhook_url,
                json=card
            )
            response.raise_for_status()
            
            log_structured(
                logger,
                "info",
                "Sent Teams custom message",
                extra={
                    "title": title,
                    "message_length": len(message),
                    "color": color
                }
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending Teams custom message: {e}")
            return False
    
    async def send_daily_summary(self, summary_data: Dict[str, Any]) -> bool:
        """
        Send a daily summary of triage activities to Teams.
        
        Args:
            summary_data: Dictionary containing summary statistics
        
        Returns:
            True if summary was sent successfully
        """
        try:
            if not self.webhook_url:
                logger.warning("Teams webhook URL not configured, skipping daily summary")
                return False
            
            # Build summary card
            card = {
                "type": "message",
                "attachments": [
                    {
                        "contentType": "application/vnd.microsoft.card.adaptive",
                        "content": {
                            "type": "AdaptiveCard",
                            "version": "1.4",
                            "body": [
                                {
                                    "type": "TextBlock",
                                    "text": "🤖 Daily AI Triage Summary",
                                    "weight": "Bolder",
                                    "size": "Large",
                                    "color": "Accent"
                                },
                                {
                                    "type": "TextBlock",
                                    "text": f"**Date:** {datetime.now().strftime('%Y-%m-%d')}",
                                    "wrap": True
                                },
                                {
                                    "type": "FactSet",
                                    "facts": [
                                        {
                                            "title": "Work Items Processed",
                                            "value": str(summary_data.get('processed_count', 0))
                                        },
                                        {
                                            "title": "Auto-Assigned",
                                            "value": str(summary_data.get('assigned_count', 0))
                                        },
                                        {
                                            "title": "Duplicates Found",
                                            "value": str(summary_data.get('duplicate_count', 0))
                                        },
                                        {
                                            "title": "High Priority Items",
                                            "value": str(summary_data.get('high_priority_count', 0))
                                        },
                                        {
                                            "title": "Average Confidence",
                                            "value": f"{summary_data.get('avg_confidence', 0):.1%}"
                                        }
                                    ]
                                }
                            ]
                        }
                    }
                ]
            }
            
            # Send to Teams webhook
            response = await self.client.post(
                self.webhook_url,
                json=card
            )
            response.raise_for_status()
            
            log_structured(
                logger,
                "info",
                "Sent Teams daily summary",
                extra={
                    "processed_count": summary_data.get('processed_count', 0),
                    "assigned_count": summary_data.get('assigned_count', 0)
                }
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending Teams daily summary: {e}")
            return False
    
    def _get_color_for_theme(self, color: str) -> str:
        """
        Map color names to Teams theme colors.
        
        Args:
            color: Color name (good, warning, attention)
        
        Returns:
            Teams color value
        """
        color_map = {
            "good": "Good",
            "warning": "Warning", 
            "attention": "Attention",
            "accent": "Accent",
            "default": "Default"
        }
        return color_map.get(color.lower(), "Default")
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
