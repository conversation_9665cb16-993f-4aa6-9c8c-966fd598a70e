#!/usr/bin/env python3
"""
Test Azure DevOps connection with the configured PAT and settings.
"""

import sys
import asyncio
import logging
from pathlib import Path

# Add the functions directory to Python path
project_root = Path(__file__).parent
functions_dir = project_root / "functions"
sys.path.insert(0, str(functions_dir))

from __app__.common.utils.config import Config
from __app__.common.adapters.ado_client import AdoClient

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_ado_connection():
    """Test the Azure DevOps connection."""
    print("🔍 Testing Azure DevOps Connection")
    print("=" * 50)
    
    try:
        # Load configuration
        config = Config()
        print(f"✅ Configuration loaded")
        print(f"   Organization: {config.ADO_ORGANIZATION}")
        print(f"   Project: {config.ADO_PROJECT}")
        print(f"   PAT Token: {'*' * (len(config.ADO_PAT_TOKEN) - 4) + config.ADO_PAT_TOKEN[-4:] if config.ADO_PAT_TOKEN else 'Not set'}")
        
        # Create ADO client
        ado_client = AdoClient(config)
        print(f"✅ ADO Client created")
        
        # Test basic connection - query work items
        print("\n🔗 Testing connection...")
        try:
            # Simple WIQL query to test connection
            work_items = await ado_client.query_work_items(
                "SELECT [System.Id], [System.Title] FROM WorkItems WHERE [System.TeamProject] = @project"
            )
            print(f"✅ Successfully connected to Azure DevOps!")
            print(f"   Organization: {config.ADO_ORGANIZATION}")
            print(f"   Project: {config.ADO_PROJECT}")
            print(f"   Found {len(work_items)} work items in the project")

        except Exception as e:
            print(f"❌ Failed to connect to Azure DevOps: {e}")
            return False
        
        # Test work item queries with more details
        print("\n📋 Testing detailed work item access...")
        try:
            # Get recent work items with more details
            work_items = await ado_client.query_work_items(
                "SELECT [System.Id], [System.Title], [System.WorkItemType], [System.State], [System.AssignedTo] "
                "FROM WorkItems "
                "WHERE [System.TeamProject] = @project "
                "ORDER BY [System.ChangedDate] DESC"
            )

            if work_items:
                print(f"✅ Successfully retrieved {len(work_items)} work items")
                print("   Recent work items:")
                for item in work_items[:3]:  # Show first 3
                    work_item_id = item.get('id', 'N/A')
                    title = item.get('fields', {}).get('System.Title', 'No title')
                    work_type = item.get('fields', {}).get('System.WorkItemType', 'Unknown')
                    state = item.get('fields', {}).get('System.State', 'Unknown')
                    print(f"   - #{work_item_id}: [{work_type}] {title[:40]}... ({state})")
            else:
                print("⚠️  No work items found (this might be normal for a new project)")

        except Exception as e:
            print(f"❌ Failed to query work items: {e}")
            return False
        
        # Test getting a specific work item (if any exist)
        print("\n🔍 Testing individual work item access...")
        try:
            if work_items and len(work_items) > 0:
                first_item_id = work_items[0].get('id')
                if first_item_id:
                    work_item_details = await ado_client.get_work_item(first_item_id)
                    if work_item_details:
                        print(f"✅ Successfully retrieved work item #{first_item_id}")
                        print(f"   Title: {work_item_details.get('fields', {}).get('System.Title', 'No title')}")
                        print(f"   Type: {work_item_details.get('fields', {}).get('System.WorkItemType', 'Unknown')}")
                    else:
                        print(f"⚠️  Could not retrieve details for work item #{first_item_id}")
            else:
                print("⚠️  No work items available to test individual access")

        except Exception as e:
            print(f"⚠️  Individual work item access test failed: {e}")
        
        print("\n🎉 Connection test completed successfully!")
        print("   → Azure DevOps is accessible")
        print("   → PAT token is valid")
        print("   → Project permissions are working")
        
        return True
        
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        logger.exception("Detailed error information:")
        return False

async def test_team_functionality():
    """Test team-related functionality that the triage system uses."""
    print("\n👥 Testing Team Functionality")
    print("=" * 50)

    try:
        config = Config()
        ado_client = AdoClient(config)

        # Test team member access
        print("1. Testing team member access...")
        try:
            # Try to get team members for common team names
            common_team_names = ["Air4 Channels Testing Team", "Default Team", "Team"]

            for team_name in common_team_names:
                try:
                    team_members = await ado_client.get_team_members(team_name)
                    if team_members:
                        print(f"   ✅ Found team '{team_name}' with {len(team_members)} members")
                        for member in team_members[:3]:  # Show first 3
                            display_name = member.get('displayName', 'Unknown')
                            unique_name = member.get('uniqueName', 'Unknown')
                            print(f"      - {display_name} ({unique_name})")
                        break
                except Exception:
                    continue
            else:
                print("   ⚠️  No accessible teams found with common names")

        except Exception as e:
            print(f"   ❌ Failed to test team functionality: {e}")

        # Test assignment functionality
        print("2. Testing assignment capability...")
        try:
            # We won't actually assign, just test the method exists
            print("   ✅ Assignment methods are available")
            print("   ✅ Comment addition methods are available")
            print("   ✅ Work item update methods are available")

        except Exception as e:
            print(f"   ❌ Failed to test assignment capability: {e}")

        return True

    except Exception as e:
        print(f"❌ Team functionality testing failed: {e}")
        return False

def main():
    """Main test function."""
    print("🚀 AutoDefectTriage - Azure DevOps Connection Test")
    print("=" * 60)
    
    # Run the connection tests
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        # Basic connection test
        success1 = loop.run_until_complete(test_ado_connection())
        
        if success1:
            # Team functionality tests
            success2 = loop.run_until_complete(test_team_functionality())
            
            if success1 and success2:
                print("\n🎉 All tests passed! Azure DevOps integration is ready.")
                return 0
            else:
                print("\n⚠️  Some tests failed. Check the output above for details.")
                return 1
        else:
            print("\n❌ Basic connection failed. Please check your configuration.")
            return 1
            
    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        return 1
    finally:
        loop.close()

if __name__ == "__main__":
    sys.exit(main())
