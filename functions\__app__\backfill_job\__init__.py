"""
Azure Function: Backfill Job
Timer trigger for building historical data and reprocessing work items.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional
import azure.functions as func

from ..common.models.schemas import WorkItem
from ..common.adapters.ado_client import Ado<PERSON><PERSON>
from ..common.adapters.search_client import Search<PERSON>lient
from ..common.ai.embeddings import EmbeddingService
from ..common.utils.config import get_config
from ..common.utils.logging import setup_logging, log_structured

# Initialize logging
setup_logging()
logger = logging.getLogger(__name__)

# Initialize clients (will be lazy-loaded)
_ado_client: Optional[AdoClient] = None
_search_client: Optional[SearchClient] = None
_embedding_service: Optional[EmbeddingService] = None


def get_clients():
    """Lazy initialization of clients."""
    global _ado_client, _search_client, _embedding_service
    
    if _ado_client is None:
        config = get_config()
        _ado_client = AdoClient(config)
        _search_client = SearchClient(config)
        _embedding_service = EmbeddingService(config)
    
    return {
        'ado': _ado_client,
        'search': _search_client,
        'embedding': _embedding_service
    }


async def main(mytimer: func.TimerRequest) -> None:
    """
    Main function for backfill job.
    Runs on a schedule to process historical work items and update the search index.
    """
    utc_timestamp = datetime.utcnow().replace(tzinfo=None).isoformat()
    
    if mytimer.past_due:
        log_structured(
            logger,
            "warning",
            "Backfill job is running late",
            extra={"timestamp": utc_timestamp}
        )
    
    log_structured(
        logger,
        "info",
        "Starting backfill job",
        extra={"timestamp": utc_timestamp}
    )
    
    try:
        clients = get_clients()
        config = get_config()
        
        # Determine the date range for backfill
        days_back = config.get('BACKFILL_DAYS_BACK', 30)
        start_date = datetime.utcnow() - timedelta(days=days_back)
        end_date = datetime.utcnow()
        
        log_structured(
            logger,
            "info",
            "Backfill date range determined",
            extra={
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "days_back": days_back
            }
        )
        
        # Fetch work items from ADO
        work_items = await fetch_work_items_for_backfill(
            clients['ado'], 
            start_date, 
            end_date
        )
        
        log_structured(
            logger,
            "info",
            "Fetched work items for backfill",
            extra={"work_item_count": len(work_items)}
        )
        
        # Process work items in batches
        batch_size = config.get('BACKFILL_BATCH_SIZE', 50)
        processed_count = 0
        failed_count = 0
        
        for i in range(0, len(work_items), batch_size):
            batch = work_items[i:i + batch_size]
            
            log_structured(
                logger,
                "info",
                "Processing backfill batch",
                extra={
                    "batch_start": i,
                    "batch_size": len(batch),
                    "total_items": len(work_items)
                }
            )
            
            batch_results = await process_work_item_batch(batch, clients)
            processed_count += batch_results['processed']
            failed_count += batch_results['failed']
        
        log_structured(
            logger,
            "info",
            "Backfill job completed",
            extra={
                "total_work_items": len(work_items),
                "processed_count": processed_count,
                "failed_count": failed_count,
                "timestamp": utc_timestamp
            }
        )
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            "Backfill job failed",
            extra={
                "error": str(e),
                "timestamp": utc_timestamp
            }
        )
        raise


async def fetch_work_items_for_backfill(
    ado_client: AdoClient,
    start_date: datetime,
    end_date: datetime
) -> List[WorkItem]:
    """
    Fetch work items from ADO for the specified date range.
    """
    try:
        # Build WIQL query for work items in date range
        wiql_query = f"""
        SELECT [System.Id], [System.Title], [System.Description], [System.WorkItemType],
               [System.State], [System.AreaPath], [System.AssignedTo], [System.CreatedDate],
               [System.ChangedDate], [Microsoft.VSTS.Common.Priority], [System.Tags]
        FROM WorkItems
        WHERE [System.CreatedDate] >= '{start_date.isoformat()}'
          AND [System.CreatedDate] <= '{end_date.isoformat()}'
          AND [System.WorkItemType] IN ('Bug', 'Task', 'User Story', 'Feature')
          AND [System.State] <> 'Removed'
        ORDER BY [System.CreatedDate] DESC
        """
        
        work_items = await ado_client.query_work_items(wiql_query)
        
        # Convert to WorkItem models
        work_item_models = []
        for item in work_items:
            try:
                work_item = WorkItem(
                    id=item.get('id'),
                    title=item.get('fields', {}).get('System.Title', ''),
                    description=item.get('fields', {}).get('System.Description', ''),
                    work_item_type=item.get('fields', {}).get('System.WorkItemType', ''),
                    state=item.get('fields', {}).get('System.State', ''),
                    area_path=item.get('fields', {}).get('System.AreaPath', ''),
                    assigned_to=item.get('fields', {}).get('System.AssignedTo', {}).get('displayName', ''),
                    created_date=item.get('fields', {}).get('System.CreatedDate', ''),
                    changed_date=item.get('fields', {}).get('System.ChangedDate', ''),
                    priority=item.get('fields', {}).get('Microsoft.VSTS.Common.Priority', 2),
                    tags=item.get('fields', {}).get('System.Tags', ''),
                )
                work_item_models.append(work_item)
            except Exception as e:
                logger.warning(f"Failed to parse work item {item.get('id')}: {e}")
                continue
        
        return work_item_models
        
    except Exception as e:
        logger.error(f"Failed to fetch work items for backfill: {e}")
        raise


async def process_work_item_batch(
    work_items: List[WorkItem],
    clients: dict
) -> dict:
    """
    Process a batch of work items for indexing.
    """
    processed = 0
    failed = 0
    
    for work_item in work_items:
        try:
            # Generate embeddings for the work item
            embedding = await clients['embedding'].embed_work_item(work_item)
            
            # Update work item with embedding
            work_item_dict = work_item.dict()
            work_item_dict['embedding'] = embedding
            
            # Upsert to search index
            await clients['search'].upsert_work_item_with_embedding(
                work_item, 
                embedding
            )
            
            processed += 1
            
            log_structured(
                logger,
                "debug",
                "Processed work item for backfill",
                extra={
                    "work_item_id": work_item.id,
                    "title": work_item.title[:50] + "..." if len(work_item.title) > 50 else work_item.title
                }
            )
            
        except Exception as e:
            failed += 1
            log_structured(
                logger,
                "warning",
                "Failed to process work item for backfill",
                extra={
                    "work_item_id": work_item.id,
                    "error": str(e)
                }
            )
    
    return {
        'processed': processed,
        'failed': failed
    }


# Timer trigger configuration
# This function runs every day at 2 AM UTC
# NCRONTAB expression: "0 0 2 * * *" (seconds, minutes, hours, day, month, day-of-week)
# For testing, you can use "0 */5 * * * *" to run every 5 minutes
