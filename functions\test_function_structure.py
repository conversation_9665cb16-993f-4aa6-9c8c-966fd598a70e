#!/usr/bin/env python3
"""
Test script to verify Azure Functions structure without heavy dependencies.
"""

import sys
import os
import importlib.util

def test_function_app_structure():
    """Test if the function app can be loaded."""
    print("Testing Azure Functions structure...")
    
    # Add the current directory to Python path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    try:
        # Test basic Azure Functions import
        import azure.functions as func
        print("✓ Azure Functions library imported successfully")
        
        # Test function app import
        from __app__.function_app import app
        print("✓ Function app imported successfully")
        print(f"  App type: {type(app)}")
        
        # Check if functions are registered
        if hasattr(app, '_function_builders'):
            builders = app._function_builders
            print(f"✓ Found {len(builders)} function builders:")
            for builder in builders:
                if hasattr(builder, '_function_name'):
                    print(f"  - {builder._function_name}")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Error: {e}")
        return False

def test_minimal_function():
    """Test a minimal function without heavy dependencies."""
    print("\nTesting minimal function...")
    
    try:
        import azure.functions as func
        
        # Create a minimal function app for testing
        test_app = func.FunctionApp()
        
        @test_app.function_name(name="test_function")
        @test_app.route(route="test", auth_level=func.AuthLevel.ANONYMOUS)
        def test_function(req: func.HttpRequest) -> func.HttpResponse:
            return func.HttpResponse("Hello from test function!", status_code=200)
        
        print("✓ Minimal function created successfully")
        return True
        
    except Exception as e:
        print(f"✗ Error creating minimal function: {e}")
        return False

if __name__ == "__main__":
    print("Azure Functions Structure Test")
    print("=" * 40)
    
    success = True
    success &= test_function_app_structure()
    success &= test_minimal_function()
    
    print("\n" + "=" * 40)
    if success:
        print("✓ All tests passed!")
        sys.exit(0)
    else:
        print("✗ Some tests failed!")
        sys.exit(1)
