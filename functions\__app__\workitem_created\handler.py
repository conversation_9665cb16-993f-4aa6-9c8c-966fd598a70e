"""
Azure Function: WorkItem Created Handler
HTTP trigger for Azure DevOps Service Hook when work items are created/updated.
"""

import json
import logging
from typing import Dict, Any, Optional
import azure.functions as func
from azure.functions import HttpRequest, HttpResponse

from ..common.models.schemas import WorkItem, TriageResult
from ..common.adapters.ado_client import AdoClient
from ..common.adapters.search_client import SearchClient
from ..common.adapters.teams_client import TeamsClient
from ..common.ai.duplicate import DuplicateDetector
from ..common.ai.assigner import AssignmentEngine
from ..common.ai.priority import PriorityEngine
from ..common.utils.config import get_config
from ..common.utils.logging import setup_logging, log_structured

# Initialize logging
setup_logging()
logger = logging.getLogger(__name__)

# Initialize clients (will be lazy-loaded)
_ado_client: Optional[AdoClient] = None
_search_client: Optional[SearchClient] = None
_teams_client: Optional[TeamsClient] = None
_duplicate_detector: Optional[DuplicateDetector] = None
_assignment_engine: Optional[AssignmentEngine] = None
_priority_engine: Optional[PriorityEngine] = None


def get_clients():
    """Lazy initialization of clients."""
    global _ado_client, _search_client, _teams_client
    global _duplicate_detector, _assignment_engine, _priority_engine
    
    if _ado_client is None:
        config = get_config()
        _ado_client = AdoClient(config)
        _search_client = SearchClient(config)
        _teams_client = TeamsClient(config)
        _duplicate_detector = DuplicateDetector(_search_client, config)
        _assignment_engine = AssignmentEngine(_search_client, config)
        _priority_engine = PriorityEngine(config)
    
    return {
        'ado': _ado_client,
        'search': _search_client,
        'teams': _teams_client,
        'duplicate': _duplicate_detector,
        'assigner': _assignment_engine,
        'priority': _priority_engine
    }


def extract_work_item_from_webhook(webhook_data: Dict[str, Any]) -> Optional[WorkItem]:
    """
    Extract work item data from ADO webhook payload and convert to WorkItem model.
    """
    try:
        # ADO webhook structure: resource.fields contains the work item data
        resource = webhook_data.get('resource', {})
        fields = resource.get('fields', {})
        
        if not fields:
            logger.warning("No fields found in webhook payload")
            return None
        
        # Extract and map fields to WorkItem model
        work_item_data = {
            'id': resource.get('id') or resource.get('workItemId'),
            'title': fields.get('System.Title', ''),
            'description': fields.get('System.Description', ''),
            'work_item_type': fields.get('System.WorkItemType', ''),
            'state': fields.get('System.State', ''),
            'area_path': fields.get('System.AreaPath', ''),
            'iteration_path': fields.get('System.IterationPath', ''),
            'assigned_to': _extract_user_display_name(fields.get('System.AssignedTo')),
            'created_by': _extract_user_display_name(fields.get('System.CreatedBy')),
            'created_date': fields.get('System.CreatedDate', ''),
            'changed_date': fields.get('System.ChangedDate', ''),
            'priority': fields.get('Microsoft.VSTS.Common.Priority', 2),
            'severity': fields.get('Microsoft.VSTS.Common.Severity', ''),
            'tags': fields.get('System.Tags', ''),
            'repro_steps': fields.get('Microsoft.VSTS.TCM.ReproSteps', ''),
            'system_info': fields.get('Microsoft.VSTS.TCM.SystemInfo', ''),
        }
        
        # Create WorkItem instance
        work_item = WorkItem(**work_item_data)
        
        log_structured(
            logger.info,
            "Extracted work item from webhook",
            work_item_id=work_item.id,
            work_item_type=work_item.work_item_type,
            title=work_item.title[:100] + "..." if len(work_item.title) > 100 else work_item.title
        )
        
        return work_item
        
    except Exception as e:
        log_structured(
            logger.error,
            "Failed to extract work item from webhook",
            error=str(e),
            webhook_keys=list(webhook_data.keys()) if webhook_data else []
        )
        return None


def _extract_user_display_name(user_field: Any) -> str:
    """Extract display name from user field (can be dict or string)."""
    if isinstance(user_field, dict):
        return user_field.get('displayName', '')
    elif isinstance(user_field, str):
        return user_field
    else:
        return ''


async def process_workitem_webhook(req: HttpRequest) -> HttpResponse:
    """
    Main function to process Azure DevOps work item webhook.
    
    This function:
    1. Extracts work item data from the webhook
    2. Runs AI triage (duplicate detection, assignment, priority)
    3. Updates the work item in ADO
    4. Sends Teams notification
    5. Indexes the work item for future searches
    """
    try:
        # Parse the webhook payload
        try:
            webhook_data = req.get_json()
        except ValueError as e:
            log_structured(
                logger.error,
                "Invalid JSON in webhook payload",
                error=str(e)
            )
            return func.HttpResponse(
                json.dumps({"error": "Invalid JSON payload"}),
                status_code=400,
                mimetype="application/json"
            )
        
        if not webhook_data:
            return func.HttpResponse(
                json.dumps({"error": "Empty payload"}),
                status_code=400,
                mimetype="application/json"
            )
        
        log_structured(
            logger.info,
            "Received webhook",
            event_type=webhook_data.get('eventType'),
            subscription_id=webhook_data.get('subscriptionId')
        )
        
        # Extract work item from webhook
        work_item = extract_work_item_from_webhook(webhook_data)
        if not work_item:
            return func.HttpResponse(
                json.dumps({"error": "Could not extract work item from webhook"}),
                status_code=400,
                mimetype="application/json"
            )
        
        # Initialize clients
        clients = get_clients()
        
        # Run AI triage pipeline
        triage_result = await run_triage_pipeline(work_item, clients)
        
        # Update work item in ADO with triage results
        await update_work_item_with_triage(work_item, triage_result, clients['ado'])
        
        # Send Teams notification
        await send_teams_notification(work_item, triage_result, clients['teams'])
        
        # Index work item for future similarity searches
        await index_work_item(work_item, clients['search'])
        
        # Return success response
        response_data = {
            "status": "success",
            "work_item_id": work_item.id,
            "triage_result": triage_result.dict() if triage_result else None
        }
        
        log_structured(
            logger.info,
            "Successfully processed work item",
            work_item_id=work_item.id,
            assigned_to=triage_result.assigned_to if triage_result else None,
            priority=triage_result.priority if triage_result else None
        )
        
        return func.HttpResponse(
            json.dumps(response_data),
            status_code=200,
            mimetype="application/json"
        )
        
    except Exception as e:
        log_structured(
            logger.error,
            "Unexpected error processing webhook",
            error=str(e),
            error_type=type(e).__name__
        )
        
        return func.HttpResponse(
            json.dumps({
                "error": "Internal server error",
                "message": str(e)
            }),
            status_code=500,
            mimetype="application/json"
        )


async def run_triage_pipeline(work_item: WorkItem, clients: Dict[str, Any]) -> Optional[TriageResult]:
    """
    Run the complete AI triage pipeline for a work item.
    """
    try:
        # Step 1: Duplicate Detection
        duplicates = await clients['duplicate'].find_duplicates(work_item)
        
        # Step 2: Priority Analysis
        priority = await clients['priority'].calculate_priority(work_item)
        
        # Step 3: Assignment
        assignment = await clients['assigner'].assign_work_item(work_item)
        
        # Create triage result
        triage_result = TriageResult(
            work_item_id=work_item.id,
            assigned_to=assignment.get('assigned_to', ''),
            priority=priority,
            duplicates=[dup.dict() for dup in duplicates] if duplicates else [],
            confidence_score=assignment.get('confidence', 0.0),
            reasoning=assignment.get('reasoning', '')
        )
        
        log_structured(
            logger.info,
            "Completed triage pipeline",
            work_item_id=work_item.id,
            duplicates_found=len(duplicates) if duplicates else 0,
            assigned_to=triage_result.assigned_to,
            priority=triage_result.priority
        )
        
        return triage_result
        
    except Exception as e:
        log_structured(
            logger.error,
            "Error in triage pipeline",
            work_item_id=work_item.id,
            error=str(e)
        )
        return None


async def update_work_item_with_triage(work_item: WorkItem, triage_result: TriageResult, ado_client: AdoClient):
    """Update the work item in ADO with triage results."""
    try:
        if not triage_result:
            return
        
        updates = {}
        
        # Update assignment if we have a confident assignment
        if triage_result.assigned_to and triage_result.confidence_score > 0.7:
            updates['System.AssignedTo'] = triage_result.assigned_to
        
        # Update priority if it changed
        if triage_result.priority != work_item.priority:
            updates['Microsoft.VSTS.Common.Priority'] = triage_result.priority
        
        # Add triage reasoning as a comment
        if triage_result.reasoning:
            comment = f"🤖 AI Triage: {triage_result.reasoning}"
            if triage_result.duplicates:
                comment += f"\n\n🔍 Potential duplicates found: {len(triage_result.duplicates)}"
            
            updates['System.History'] = comment
        
        if updates:
            await ado_client.update_work_item(work_item.id, updates)
            log_structured(
                logger.info,
                "Updated work item with triage results",
                work_item_id=work_item.id,
                updates=list(updates.keys())
            )
        
    except Exception as e:
        log_structured(
            logger.error,
            "Failed to update work item",
            work_item_id=work_item.id,
            error=str(e)
        )


async def send_teams_notification(work_item: WorkItem, triage_result: TriageResult, teams_client: TeamsClient):
    """Send notification to Teams channel."""
    try:
        if not triage_result:
            return
        
        await teams_client.send_triage_notification(work_item, triage_result)
        
        log_structured(
            logger.info,
            "Sent Teams notification",
            work_item_id=work_item.id
        )
        
    except Exception as e:
        log_structured(
            logger.error,
            "Failed to send Teams notification",
            work_item_id=work_item.id,
            error=str(e)
        )


async def index_work_item(work_item: WorkItem, search_client: SearchClient):
    """Index the work item for future similarity searches."""
    try:
        await search_client.index_work_item(work_item)
        
        log_structured(
            logger.info,
            "Indexed work item for search",
            work_item_id=work_item.id
        )
        
    except Exception as e:
        log_structured(
            logger.error,
            "Failed to index work item",
            work_item_id=work_item.id,
            error=str(e)
        )
