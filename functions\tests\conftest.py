"""
Pytest configuration and shared fixtures for the QA AI Triage system tests.
"""

import pytest
import asyncio
import os
import sys
from unittest.mock import Mock, AsyncMock
from typing import Dict, Any

# Add the functions directory to the path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from __app__.common.models.schemas import WorkItem, SearchResult
from __app__.common.utils.config import Config


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_config():
    """Mock configuration for testing."""
    config = Mock(spec=Config)
    
    # Default configuration values
    default_config = {
        # Azure DevOps
        'ADO_ORGANIZATION': 'test-org',
        'ADO_PROJECT': 'test-project',
        'ADO_PAT_TOKEN': 'test-token',
        
        # Azure Search
        'AZURE_SEARCH_SERVICE_NAME': 'test-search',
        'AZURE_SEARCH_ADMIN_KEY': 'test-key',
        'AZURE_SEARCH_INDEX_NAME': 'workitems',
        
        # Embedding settings
        'EMBEDDING_PROVIDER': 'sentence_transformers',
        'EMBEDDING_MODEL': 'microsoft/DialoGPT-medium',
        'EMBEDDING_DIMENSION': 384,
        
        # Duplicate detection
        'DUPLICATE_SIMILARITY_THRESHOLD': 0.85,
        'DUPLICATE_TEXT_THRESHOLD': 0.8,
        'DUPLICATE_MAX_CANDIDATES': 10,
        'DUPLICATE_MAX_RESULTS': 5,
        'DUPLICATE_INCLUDE_CLOSED': False,
        
        # Assignment
        'ASSIGNMENT_KNN_K': 10,
        'ASSIGNMENT_MIN_CONFIDENCE': 0.6,
        'ASSIGNMENT_LOAD_WEIGHT': 0.3,
        'ASSIGNMENT_OWNERSHIP_WEIGHT': 0.4,
        'ASSIGNMENT_SIMILARITY_WEIGHT': 0.3,
        
        # Priority
        'PRIORITY_SEVERITY_KEYWORDS': 'critical,urgent,blocker,crash,security',
        'PRIORITY_BUSINESS_KEYWORDS': 'revenue,customer,production,release',
        
        # Teams
        'TEAMS_WEBHOOK_URL': 'https://test.webhook.url',
        
        # Key Vault
        'KEY_VAULT_URL': 'https://test-kv.vault.azure.net/',
        
        # Logging
        'LOG_LEVEL': 'INFO',
        'APPINSIGHTS_INSTRUMENTATIONKEY': 'test-key'
    }
    
    config.get.side_effect = lambda key, default=None: default_config.get(key, default)
    config.get_all.return_value = default_config
    
    return config


@pytest.fixture
def sample_work_item():
    """Sample work item for testing."""
    return WorkItem(
        id=12345,
        title="Sample bug report",
        description="This is a sample bug description for testing purposes.",
        work_item_type="Bug",
        state="New",
        area_path="TestProject\\Components\\UI",
        assigned_to="",
        created_by="<EMAIL>",
        created_date="2024-01-15T10:00:00Z",
        changed_date="2024-01-15T10:00:00Z",
        priority=2,
        tags="ui, bug, test",
        repro_steps="1. Open application\n2. Click button\n3. Observe error",
        system_info="Windows 10, Chrome 120"
    )


@pytest.fixture
def sample_assigned_work_item():
    """Sample work item that is already assigned."""
    return WorkItem(
        id=54321,
        title="Assigned bug report",
        description="This bug is already assigned to someone.",
        work_item_type="Bug",
        state="Active",
        area_path="TestProject\\Components\\API",
        assigned_to="<EMAIL>",
        created_by="<EMAIL>",
        created_date="2024-01-10T09:00:00Z",
        changed_date="2024-01-15T14:30:00Z",
        priority=1,
        tags="api, bug, assigned",
        repro_steps="1. Call API\n2. Check response\n3. Error occurs",
        system_info="Linux, curl"
    )


@pytest.fixture
def sample_search_results():
    """Sample search results for testing."""
    return [
        SearchResult(
            work_item_id=11111,
            title="Similar bug in UI component",
            description="UI component showing similar behavior",
            work_item_type="Bug",
            area_path="TestProject\\Components\\UI",
            score=8.5,
            reranker_score=0.92,
            highlights={"title": ["Similar bug"]}
        ),
        SearchResult(
            work_item_id=22222,
            title="API endpoint error",
            description="Error in API endpoint processing",
            work_item_type="Bug",
            area_path="TestProject\\Components\\API",
            score=7.2,
            reranker_score=0.78,
            highlights={"description": ["API endpoint"]}
        ),
        SearchResult(
            work_item_id=33333,
            title="Database connection issue",
            description="Database connection timing out",
            work_item_type="Bug",
            area_path="TestProject\\Database",
            score=5.1,
            reranker_score=0.65,
            highlights={}
        )
    ]


@pytest.fixture
def mock_ado_webhook_payload():
    """Mock Azure DevOps webhook payload."""
    return {
        "subscriptionId": "test-subscription-id",
        "notificationId": 1,
        "id": "test-event-id",
        "eventType": "workitem.created",
        "publisherId": "tfs",
        "message": {
            "text": "Bug #12345 (Sample bug report) created by Test User"
        },
        "detailedMessage": {
            "text": "Bug #12345 (Sample bug report) created by Test User"
        },
        "resource": {
            "id": 12345,
            "workItemId": 12345,
            "rev": 1,
            "revisedBy": {
                "displayName": "Test User",
                "uniqueName": "<EMAIL>"
            },
            "revisedDate": "2024-01-15T10:00:00Z",
            "fields": {
                "System.Id": {"newValue": 12345},
                "System.Title": {"newValue": "Sample bug report"},
                "System.Description": {"newValue": "This is a sample bug description"},
                "System.WorkItemType": {"newValue": "Bug"},
                "System.State": {"newValue": "New"},
                "System.AreaPath": {"newValue": "TestProject\\Components\\UI"},
                "System.CreatedBy": {"newValue": {"displayName": "Test User"}},
                "System.CreatedDate": {"newValue": "2024-01-15T10:00:00Z"},
                "Microsoft.VSTS.Common.Priority": {"newValue": 2}
            }
        },
        "resourceVersion": "1.0",
        "resourceContainers": {
            "collection": {
                "id": "test-collection-id"
            },
            "account": {
                "id": "test-account-id"
            },
            "project": {
                "id": "test-project-id"
            }
        },
        "createdDate": "2024-01-15T10:00:00Z"
    }


@pytest.fixture
def mock_embedding_vector():
    """Mock embedding vector for testing."""
    return [0.1] * 384  # Standard dimension for sentence transformers


@pytest.fixture
def mock_codeowners_content():
    """Mock CODEOWNERS file content."""
    return """
# Global owners
* @global-team

# API components
/src/api/ @api-team @backend-developers
/src/api/auth/ @auth-team
/src/api/users/ @user-service-team

# UI components
/src/ui/ @frontend-team
/src/ui/components/ @ui-component-team

# Database
/src/database/ @database-team @backend-developers

# Infrastructure
/infra/ @devops-team
/docker/ @devops-team

# Documentation
/docs/ @documentation-team
*.md @documentation-team
"""


@pytest.fixture
def mock_teams_adaptive_card():
    """Mock Teams Adaptive Card for testing."""
    return {
        "type": "AdaptiveCard",
        "version": "1.4",
        "body": [
            {
                "type": "TextBlock",
                "text": "New Work Item Assigned",
                "weight": "Bolder",
                "size": "Medium"
            },
            {
                "type": "FactSet",
                "facts": [
                    {"title": "ID", "value": "12345"},
                    {"title": "Title", "value": "Sample bug report"},
                    {"title": "Type", "value": "Bug"},
                    {"title": "Priority", "value": "2"},
                    {"title": "Assigned To", "value": "<EMAIL>"}
                ]
            }
        ],
        "actions": [
            {
                "type": "Action.OpenUrl",
                "title": "View Work Item",
                "url": "https://dev.azure.com/test-org/test-project/_workitems/edit/12345"
            }
        ]
    }


@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch):
    """Set up test environment variables."""
    test_env_vars = {
        'AZURE_FUNCTIONS_ENVIRONMENT': 'Development',
        'WEBSITE_SITE_NAME': 'test-function-app',
        'FUNCTIONS_WORKER_RUNTIME': 'python'
    }
    
    for key, value in test_env_vars.items():
        monkeypatch.setenv(key, value)


@pytest.fixture
def mock_async_context_manager():
    """Helper fixture for creating async context managers in tests."""
    class MockAsyncContextManager:
        def __init__(self, return_value=None):
            self.return_value = return_value
        
        async def __aenter__(self):
            return self.return_value
        
        async def __aexit__(self, exc_type, exc_val, exc_tb):
            pass
    
    return MockAsyncContextManager


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "external: mark test as requiring external services"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test names."""
    for item in items:
        # Mark integration tests
        if "integration" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        
        # Mark slow tests
        if "slow" in item.nodeid or "performance" in item.nodeid:
            item.add_marker(pytest.mark.slow)
        
        # Mark external tests
        if any(keyword in item.nodeid for keyword in ["external", "e2e", "live"]):
            item.add_marker(pytest.mark.external)
