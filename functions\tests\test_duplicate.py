"""
Tests for duplicate detection functionality.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from typing import List

from __app__.common.models.schemas import WorkItem, SearchResult, DuplicateHit
from __app__.common.ai.duplicate import DuplicateDetector
from __app__.common.adapters.search_client import Search<PERSON>lient
from __app__.common.utils.config import Config


@pytest.fixture
def mock_config():
    """Mock configuration for testing."""
    config = Mock(spec=Config)
    config.get.side_effect = lambda key, default=None: {
        'DUPLICATE_SIMILARITY_THRESHOLD': 0.85,
        'DUPLICATE_TEXT_THRESHOLD': 0.8,
        'DUPLICATE_MAX_CANDIDATES': 10,
        'DUPLICATE_MAX_RESULTS': 5,
        'DUPLICATE_INCLUDE_CLOSED': False,
        'DUPLICATE_WEIGHT_SEMANTIC': 0.4,
        'DUPLICATE_WEIGHT_TEXT': 0.3,
        'DUPLICATE_WEIGHT_SIGNATURE': 0.2,
        'DUPLICATE_WEIGHT_METADATA': 0.1
    }.get(key, default)
    return config


@pytest.fixture
def mock_search_client():
    """Mock search client for testing."""
    return Mock(spec=SearchClient)


@pytest.fixture
def sample_work_item():
    """Sample work item for testing."""
    return WorkItem(
        id=12345,
        title="Login page crashes when clicking submit button",
        description="When users click the submit button on the login page, the application crashes with a null pointer exception.",
        work_item_type="Bug",
        state="New",
        area_path="MyProject\\Authentication\\Login",
        assigned_to="",
        created_by="<EMAIL>",
        created_date="2024-01-15T10:00:00Z",
        changed_date="2024-01-15T10:00:00Z",
        priority=2,
        tags="login, crash, authentication",
        repro_steps="1. Navigate to login page\n2. Enter credentials\n3. Click submit\n4. Application crashes",
        system_info="Windows 10, Chrome 120"
    )


@pytest.fixture
def sample_search_results():
    """Sample search results for testing."""
    return [
        SearchResult(
            work_item_id=11111,
            title="Login form crashes on submit",
            description="Application crashes when submitting login form",
            work_item_type="Bug",
            area_path="MyProject\\Authentication\\Login",
            score=8.5,
            reranker_score=0.92,
            highlights={"title": ["Login form crashes"]}
        ),
        SearchResult(
            work_item_id=22222,
            title="Submit button not working in registration",
            description="Registration form submit button is not responsive",
            work_item_type="Bug",
            area_path="MyProject\\Authentication\\Registration",
            score=6.2,
            reranker_score=0.75,
            highlights={"title": ["Submit button"]}
        ),
        SearchResult(
            work_item_id=33333,
            title="Database connection timeout",
            description="Database queries are timing out after 30 seconds",
            work_item_type="Bug",
            area_path="MyProject\\Database",
            score=3.1,
            reranker_score=0.45,
            highlights={}
        )
    ]


class TestDuplicateDetector:
    """Test cases for DuplicateDetector class."""
    
    def test_init(self, mock_search_client, mock_config):
        """Test DuplicateDetector initialization."""
        detector = DuplicateDetector(mock_search_client, mock_config)
        
        assert detector.search_client == mock_search_client
        assert detector.config == mock_config
        assert detector.similarity_threshold == 0.85
        assert detector.max_candidates == 10
    
    @pytest.mark.asyncio
    async def test_find_duplicates_no_candidates(self, mock_search_client, mock_config, sample_work_item):
        """Test duplicate detection when no candidates are found."""
        detector = DuplicateDetector(mock_search_client, mock_config)
        
        # Mock embedding service
        with patch.object(detector, 'embedding_service') as mock_embedding:
            mock_embedding.embed_work_item.return_value = [0.1] * 384
            
            # Mock search client to return no results
            mock_search_client.hybrid_search = AsyncMock(return_value=[])
            
            duplicates = await detector.find_duplicates(sample_work_item)
            
            assert duplicates == []
            mock_embedding.embed_work_item.assert_called_once_with(sample_work_item)
            mock_search_client.hybrid_search.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_find_duplicates_with_candidates(self, mock_search_client, mock_config, sample_work_item, sample_search_results):
        """Test duplicate detection with candidate results."""
        detector = DuplicateDetector(mock_search_client, mock_config)
        
        # Mock embedding service
        with patch.object(detector, 'embedding_service') as mock_embedding:
            mock_embedding.embed_work_item.return_value = [0.1] * 384
            
            # Mock search client to return sample results
            mock_search_client.hybrid_search = AsyncMock(return_value=sample_search_results)
            
            # Mock text similarity calculation
            with patch('functions.__app__.common.utils.text.calculate_text_similarity') as mock_text_sim:
                mock_text_sim.return_value = 0.9
                
                # Mock signature extraction
                with patch('functions.__app__.common.utils.text.extract_signature') as mock_signature:
                    mock_signature.return_value = "login_crash_signature"
                    
                    duplicates = await detector.find_duplicates(sample_work_item)
                    
                    assert len(duplicates) > 0
                    assert all(isinstance(dup, DuplicateHit) for dup in duplicates)
                    
                    # Check that the most similar item is first
                    if len(duplicates) > 1:
                        assert duplicates[0].similarity_score >= duplicates[1].similarity_score
    
    @pytest.mark.asyncio
    async def test_find_candidates_filters(self, mock_search_client, mock_config, sample_work_item):
        """Test that candidate search applies correct filters."""
        detector = DuplicateDetector(mock_search_client, mock_config)
        
        with patch.object(detector, 'embedding_service') as mock_embedding:
            mock_embedding.embed_work_item.return_value = [0.1] * 384
            
            mock_search_client.hybrid_search = AsyncMock(return_value=[])
            
            await detector._find_candidates(sample_work_item, [0.1] * 384)
            
            # Verify search was called with correct filters
            call_args = mock_search_client.hybrid_search.call_args
            assert call_args is not None
            
            # Check that filters exclude the same work item
            filters = call_args.kwargs.get('filters', '')
            assert f"id ne '{sample_work_item.id}'" in filters
            
            # Check that closed items are excluded by default
            assert "state ne 'Closed'" in filters
    
    def test_calculate_composite_score(self, mock_search_client, mock_config):
        """Test composite score calculation."""
        detector = DuplicateDetector(mock_search_client, mock_config)
        
        # Test high similarity case
        score = detector._calculate_composite_score(
            semantic_score=8.5,
            text_similarity=0.9,
            signature_match=True,
            area_path_match=True,
            type_match=True
        )
        
        assert 0.0 <= score <= 1.0
        assert score > 0.8  # Should be high for good matches
        
        # Test low similarity case
        score_low = detector._calculate_composite_score(
            semantic_score=2.0,
            text_similarity=0.3,
            signature_match=False,
            area_path_match=False,
            type_match=False
        )
        
        assert score_low < score  # Should be lower than high similarity case
    
    def test_build_match_reasons(self, mock_search_client, mock_config):
        """Test match reason generation."""
        detector = DuplicateDetector(mock_search_client, mock_config)
        
        from functions.__app__.common.ai.duplicate import DuplicateCandidate
        
        candidate = DuplicateCandidate(
            work_item_id=11111,
            title="Test title",
            description="Test description",
            similarity_score=0.9,
            text_similarity=0.85,
            signature_match=True,
            area_path_match=True,
            type_match=True
        )
        
        reasons = detector._build_match_reasons(candidate)
        
        assert len(reasons) > 0
        assert any("text similarity" in reason.lower() for reason in reasons)
        assert any("signature" in reason.lower() for reason in reasons)
        assert any("area path" in reason.lower() for reason in reasons)
    
    def test_build_type_filter(self, mock_search_client, mock_config):
        """Test work item type filter generation."""
        detector = DuplicateDetector(mock_search_client, mock_config)
        
        # Test Bug type
        bug_filter = detector._build_type_filter("Bug")
        assert "Bug" in bug_filter
        assert "Defect" in bug_filter
        
        # Test Task type
        task_filter = detector._build_type_filter("Task")
        assert "Task" in task_filter
        assert "User Story" in task_filter
        
        # Test unknown type
        unknown_filter = detector._build_type_filter("Unknown")
        assert "Unknown" in unknown_filter


@pytest.mark.asyncio
async def test_integration_duplicate_detection(mock_config):
    """Integration test for duplicate detection workflow."""
    # Create a more realistic test scenario
    work_item = WorkItem(
        id=99999,
        title="Application crashes on login",
        description="The application crashes when users try to log in",
        work_item_type="Bug",
        state="New",
        area_path="MyProject\\Authentication",
        priority=1
    )
    
    # Mock search client with realistic responses
    mock_search_client = Mock(spec=SearchClient)
    mock_search_client.hybrid_search = AsyncMock(return_value=[
        SearchResult(
            work_item_id=88888,
            title="Login crash issue",
            description="Users experiencing crashes during login",
            work_item_type="Bug",
            area_path="MyProject\\Authentication",
            score=9.2,
            reranker_score=0.95
        )
    ])
    
    detector = DuplicateDetector(mock_search_client, mock_config)
    
    # Mock the embedding service
    with patch.object(detector, 'embedding_service') as mock_embedding:
        mock_embedding.embed_work_item.return_value = [0.1] * 384
        
        # Mock text processing functions
        with patch('functions.__app__.common.utils.text.calculate_text_similarity', return_value=0.92):
            with patch('functions.__app__.common.utils.text.extract_signature', return_value="login_crash"):
                
                duplicates = await detector.find_duplicates(work_item)
                
                assert len(duplicates) == 1
                assert duplicates[0].work_item_id == 88888
                assert duplicates[0].similarity_score > 0.8
                assert len(duplicates[0].match_reasons) > 0


if __name__ == "__main__":
    pytest.main([__file__])
