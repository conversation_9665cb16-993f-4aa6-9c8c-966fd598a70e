# How to Run the WorkItem Created Function

This guide explains how to run and test the Azure Function that processes created bugs and work items.

## 🚀 Quick Start

### 1. Install Dependencies

First, make sure all dependencies are installed:

```bash
# Install Python dependencies
pip install -r functions/requirements.txt

# Install Azure Functions Core Tools (if not already installed)
npm install -g azure-functions-core-tools@4 --unsafe-perm true
```

### 2. Configure Environment Variables

Update the `functions/local.settings.json` file with your actual values:

```json
{
  "Values": {
    "ADO_ORGANIZATION": "your-actual-ado-org",
    "ADO_PROJECT": "your-actual-project", 
    "ADO_PAT_TOKEN": "your-actual-pat-token",
    "AZURE_SEARCH_SERVICE_NAME": "your-search-service",
    "AZURE_SEARCH_ADMIN_KEY": "your-search-key",
    "TEAMS_WEBHOOK_URL": "your-teams-webhook-url"
  }
}
```

### 3. Run Locally with Azure Functions Core Tools

```bash
# Navigate to the functions directory
cd functions

# Start the Azure Functions runtime
func start
```

The function will be available at: `http://localhost:7071/api/workitem_created`

### 4. Test with Sample Data

Run the test script to simulate an Azure DevOps webhook:

```bash
# From the functions directory
python test_workitem_function.py
```

## 📡 How the Function Works

### Function Trigger
The function is triggered by HTTP POST requests from Azure DevOps Service Hooks when:
- A work item is created
- A work item is updated
- A bug is reported

### Processing Pipeline
When a bug is created, the function:

1. **Receives Webhook** - Gets the ADO webhook payload
2. **Extracts Work Item** - Parses work item data from the payload
3. **Duplicate Detection** - Searches for similar existing work items
4. **Priority Calculation** - Determines priority based on content analysis
5. **Assignment** - Assigns to appropriate team member based on expertise
6. **Updates ADO** - Updates the work item with triage results
7. **Teams Notification** - Sends notification to Teams channel
8. **Indexes for Search** - Adds to search index for future similarity matching

### Sample Webhook Payload

The function expects Azure DevOps webhook payloads like this:

```json
{
  "eventType": "workitem.created",
  "resource": {
    "id": 123,
    "fields": {
      "System.Title": "Application crashes when clicking submit",
      "System.Description": "Detailed description...",
      "System.WorkItemType": "Bug",
      "System.State": "New",
      "Microsoft.VSTS.Common.Priority": 2,
      "Microsoft.VSTS.Common.Severity": "2 - High"
    }
  }
}
```

## 🧪 Testing Options

### Option 1: Local Test Script
```bash
python functions/test_workitem_function.py
```

### Option 2: HTTP Request with curl
```bash
curl -X POST http://localhost:7071/api/workitem_created \
  -H "Content-Type: application/json" \
  -d @sample_webhook.json
```

### Option 3: Postman/Insomnia
- URL: `http://localhost:7071/api/workitem_created`
- Method: POST
- Headers: `Content-Type: application/json`
- Body: Use the sample webhook payload from the test script

## 🔧 Configuration

### Required Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `ADO_ORGANIZATION` | Azure DevOps organization | `mycompany` |
| `ADO_PROJECT` | Azure DevOps project name | `MyProject` |
| `ADO_PAT_TOKEN` | Personal Access Token | `pat_token_here` |
| `AZURE_SEARCH_SERVICE_NAME` | AI Search service name | `mysearch` |
| `AZURE_SEARCH_ADMIN_KEY` | AI Search admin key | `search_key_here` |
| `TEAMS_WEBHOOK_URL` | Teams webhook URL | `https://outlook.office.com/webhook/...` |

### Optional Configuration

| Variable | Description | Default |
|----------|-------------|---------|
| `EMBEDDING_PROVIDER` | AI provider for embeddings | `sentence_transformers` |
| `EMBEDDING_MODEL` | Model for embeddings | `microsoft/E5-large-v2` |
| `LOG_LEVEL` | Logging level | `INFO` |

## 🐛 Troubleshooting

### Common Issues

1. **Function not starting**
   - Check that Azure Functions Core Tools are installed
   - Verify Python version (3.8-3.11 supported)
   - Check `functions/requirements.txt` dependencies

2. **Import errors**
   - Ensure all dependencies are installed
   - Check Python path configuration
   - Verify file structure matches expected layout

3. **Configuration errors**
   - Verify `local.settings.json` has correct values
   - Check environment variable names match exactly
   - Ensure secrets are properly configured

4. **Webhook payload issues**
   - Verify the payload structure matches ADO format
   - Check that required fields are present
   - Validate JSON format

### Debug Mode

Enable debug logging by setting:
```json
{
  "Values": {
    "LOG_LEVEL": "DEBUG"
  }
}
```

## 🔗 Azure DevOps Service Hook Setup

To receive real webhooks from Azure DevOps:

1. Go to your ADO project settings
2. Navigate to Service Hooks
3. Create a new subscription
4. Select "Work item created" or "Work item updated"
5. Set the URL to your function endpoint
6. Configure filters as needed

## 📊 Monitoring

The function logs structured data that can be monitored in:
- Azure Application Insights (when deployed)
- Local console output (during development)
- Azure Functions logs

Key metrics tracked:
- Work item processing time
- Assignment accuracy
- Duplicate detection rate
- Error rates and types
