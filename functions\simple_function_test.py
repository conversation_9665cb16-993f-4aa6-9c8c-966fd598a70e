#!/usr/bin/env python3
"""
Simple test to verify Azure Functions v2 programming model works.
"""

import azure.functions as func
import logging

# Create the function app
app = func.FunctionApp()

# Configure logging
logging.basicConfig(level=logging.INFO)


@app.function_name(name="simple_test")
@app.route(route="simple_test", auth_level=func.AuthLevel.ANONYMOUS)
def simple_test_trigger(req: func.HttpRequest) -> func.HttpResponse:
    """
    Simple HTTP trigger for testing.
    """
    logging.info('Python HTTP trigger function processed a request.')

    name = req.params.get('name')
    if not name:
        try:
            req_body = req.get_json()
        except ValueError:
            pass
        else:
            name = req_body.get('name')

    if name:
        return func.HttpResponse(f"Hello, {name}! This HTTP triggered function executed successfully.")
    else:
        return func.HttpResponse(
             "This HTTP triggered function executed successfully. Pass a name in the query string or in the request body for a personalized response.",
             status_code=200
        )


if __name__ == "__main__":
    print("Simple function app created successfully")
    print(f"App type: {type(app)}")
    if hasattr(app, '_function_builders'):
        print(f"Function builders: {len(app._function_builders)}")
