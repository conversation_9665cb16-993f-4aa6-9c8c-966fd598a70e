"""
Assignment Engine
Assigns work items to team members using kNN voting, ownership rules, and load balancing.
"""

import logging
from typing import Dict, List, Optional, Tuple, Any
from collections import defaultdict, Counter
from dataclasses import dataclass
from datetime import datetime, timedelta

from ..models.schemas import WorkItem, SearchResult
from ..adapters.search_client import SearchClient
from ..adapters.ado_client import AdoClient
from ..ai.embeddings import EmbeddingService
from ..ownership.codeowners import CodeOwnersParser
from ..ownership.heuristics import OwnershipHeuristics
from ..utils.config import Config
from ..utils.logging import log_structured

logger = logging.getLogger(__name__)


@dataclass
class AssignmentCandidate:
    """Represents a potential assignee for a work item."""
    assignee: str
    confidence_score: float
    reasoning: List[str]
    vote_count: int
    ownership_match: bool
    load_penalty: float


@dataclass
class TeamMemberLoad:
    """Represents current workload for a team member."""
    assignee: str
    active_items: int
    recent_assignments: int
    avg_resolution_time: float
    expertise_areas: List[str]


class AssignmentEngine:
    """Engine for automatically assigning work items to team members."""
    
    def __init__(self, search_client: SearchClient, config: Config):
        self.search_client = search_client
        self.config = config
        self.embedding_service = EmbeddingService(config)
        self.codeowners_parser = CodeOwnersParser(config)
        self.ownership_heuristics = OwnershipHeuristics(config)
        
        # Configuration parameters
        self.knn_k = config.get('ASSIGNMENT_KNN_K', 10)
        self.min_confidence = config.get('ASSIGNMENT_MIN_CONFIDENCE', 0.6)
        self.load_balance_weight = config.get('ASSIGNMENT_LOAD_WEIGHT', 0.3)
        self.ownership_weight = config.get('ASSIGNMENT_OWNERSHIP_WEIGHT', 0.4)
        self.similarity_weight = config.get('ASSIGNMENT_SIMILARITY_WEIGHT', 0.3)
        
        # Cache for team member loads
        self._load_cache: Dict[str, TeamMemberLoad] = {}
        self._cache_timestamp: Optional[datetime] = None
        self._cache_ttl = timedelta(minutes=30)
    
    async def assign_work_item(self, work_item: WorkItem) -> Optional[str]:
        """
        Assign a work item to the most appropriate team member.
        
        Args:
            work_item: The work item to assign
        
        Returns:
            Email/username of the assigned person, or None if no suitable assignee found
        """
        try:
            log_structured(
                logger,
                "info",
                "Starting work item assignment",
                extra={
                    "work_item_id": work_item.id,
                    "work_item_type": work_item.work_item_type,
                    "area_path": work_item.area_path
                }
            )
            
            # Get assignment candidates using multiple strategies
            candidates = await self._get_assignment_candidates(work_item)
            
            if not candidates:
                logger.warning(f"No assignment candidates found for work item {work_item.id}")
                return None
            
            # Select the best candidate
            best_candidate = self._select_best_candidate(candidates)
            
            if best_candidate.confidence_score < self.min_confidence:
                log_structured(
                    logger,
                    "warning",
                    "Assignment confidence too low",
                    extra={
                        "work_item_id": work_item.id,
                        "best_confidence": best_candidate.confidence_score,
                        "min_confidence": self.min_confidence
                    }
                )
                return None
            
            log_structured(
                logger,
                "info",
                "Work item assigned",
                extra={
                    "work_item_id": work_item.id,
                    "assignee": best_candidate.assignee,
                    "confidence": best_candidate.confidence_score,
                    "reasoning": best_candidate.reasoning
                }
            )
            
            return best_candidate.assignee
            
        except Exception as e:
            logger.error(f"Error assigning work item {work_item.id}: {e}")
            return None
    
    async def _get_assignment_candidates(self, work_item: WorkItem) -> List[AssignmentCandidate]:
        """
        Get assignment candidates using multiple strategies.
        """
        candidates = {}
        
        # Strategy 1: kNN voting based on similar work items
        knn_candidates = await self._get_knn_candidates(work_item)
        for candidate in knn_candidates:
            if candidate.assignee not in candidates:
                candidates[candidate.assignee] = candidate
            else:
                # Merge with existing candidate
                existing = candidates[candidate.assignee]
                existing.vote_count += candidate.vote_count
                existing.reasoning.extend(candidate.reasoning)
        
        # Strategy 2: Code ownership rules
        ownership_candidates = await self._get_ownership_candidates(work_item)
        for candidate in ownership_candidates:
            if candidate.assignee not in candidates:
                candidates[candidate.assignee] = candidate
            else:
                existing = candidates[candidate.assignee]
                existing.ownership_match = True
                existing.reasoning.append("Code ownership match")
        
        # Strategy 3: Heuristic rules
        heuristic_candidates = await self._get_heuristic_candidates(work_item)
        for candidate in heuristic_candidates:
            if candidate.assignee not in candidates:
                candidates[candidate.assignee] = candidate
            else:
                existing = candidates[candidate.assignee]
                existing.reasoning.extend(candidate.reasoning)
        
        # Apply load balancing
        await self._apply_load_balancing(list(candidates.values()))
        
        # Calculate final confidence scores
        for candidate in candidates.values():
            candidate.confidence_score = self._calculate_confidence_score(candidate)
        
        return list(candidates.values())
    
    async def _get_knn_candidates(self, work_item: WorkItem) -> List[AssignmentCandidate]:
        """
        Get assignment candidates using k-nearest neighbors voting.
        """
        try:
            # Generate embedding for the work item
            embedding = await self.embedding_service.embed_work_item(work_item)
            
            # Find similar work items
            similar_items = await self.search_client.hybrid_search(
                query_text=f"{work_item.title} {work_item.description or ''}",
                query_vector=embedding,
                filters=f"assigned_to ne '' and state eq 'Closed'",  # Only completed items with assignees
                top=self.knn_k
            )
            
            # Count votes for each assignee
            assignee_votes = Counter()
            assignee_scores = defaultdict(list)
            
            for item in similar_items:
                if item.work_item_id != work_item.id and hasattr(item, 'assigned_to'):
                    assignee = getattr(item, 'assigned_to', None)
                    if assignee:
                        assignee_votes[assignee] += 1
                        assignee_scores[assignee].append(item.score)
            
            # Create candidates from votes
            candidates = []
            for assignee, vote_count in assignee_votes.items():
                avg_score = sum(assignee_scores[assignee]) / len(assignee_scores[assignee])
                
                candidate = AssignmentCandidate(
                    assignee=assignee,
                    confidence_score=0.0,  # Will be calculated later
                    reasoning=[f"kNN voting: {vote_count} similar items"],
                    vote_count=vote_count,
                    ownership_match=False,
                    load_penalty=0.0
                )
                candidates.append(candidate)
            
            return candidates
            
        except Exception as e:
            logger.error(f"Error in kNN candidate selection: {e}")
            return []
    
    async def _get_ownership_candidates(self, work_item: WorkItem) -> List[AssignmentCandidate]:
        """
        Get assignment candidates based on code ownership rules.
        """
        try:
            # Parse area path to determine code ownership
            owners = await self.codeowners_parser.get_owners_for_area(work_item.area_path)
            
            candidates = []
            for owner in owners:
                candidate = AssignmentCandidate(
                    assignee=owner,
                    confidence_score=0.0,
                    reasoning=["Code ownership match"],
                    vote_count=0,
                    ownership_match=True,
                    load_penalty=0.0
                )
                candidates.append(candidate)
            
            return candidates
            
        except Exception as e:
            logger.error(f"Error in ownership candidate selection: {e}")
            return []
    
    async def _get_heuristic_candidates(self, work_item: WorkItem) -> List[AssignmentCandidate]:
        """
        Get assignment candidates using heuristic rules.
        """
        try:
            # Use heuristics to suggest assignees
            suggestions = await self.ownership_heuristics.suggest_assignees(work_item)
            
            candidates = []
            for suggestion in suggestions:
                candidate = AssignmentCandidate(
                    assignee=suggestion['assignee'],
                    confidence_score=0.0,
                    reasoning=[suggestion['reason']],
                    vote_count=0,
                    ownership_match=False,
                    load_penalty=0.0
                )
                candidates.append(candidate)
            
            return candidates
            
        except Exception as e:
            logger.error(f"Error in heuristic candidate selection: {e}")
            return []
    
    async def _apply_load_balancing(self, candidates: List[AssignmentCandidate]) -> None:
        """
        Apply load balancing penalties to candidates.
        """
        try:
            # Get current team member loads
            loads = await self._get_team_member_loads([c.assignee for c in candidates])
            
            # Calculate load penalties
            if loads:
                max_load = max(load.active_items for load in loads.values())
                
                for candidate in candidates:
                    if candidate.assignee in loads:
                        load = loads[candidate.assignee]
                        # Penalty based on relative load
                        if max_load > 0:
                            candidate.load_penalty = load.active_items / max_load
                        else:
                            candidate.load_penalty = 0.0
            
        except Exception as e:
            logger.error(f"Error applying load balancing: {e}")
    
    async def _get_team_member_loads(self, assignees: List[str]) -> Dict[str, TeamMemberLoad]:
        """
        Get current workload information for team members.
        """
        try:
            # Check cache first
            if (self._cache_timestamp and 
                datetime.now() - self._cache_timestamp < self._cache_ttl):
                return {k: v for k, v in self._load_cache.items() if k in assignees}
            
            # Refresh cache
            loads = {}
            
            for assignee in assignees:
                # Query for active work items assigned to this person
                active_query = f"assigned_to eq '{assignee}' and state ne 'Closed' and state ne 'Resolved'"
                active_items = await self.search_client.hybrid_search(
                    query_text="*",
                    filters=active_query,
                    top=100
                )
                
                # Query for recently completed items (last 30 days)
                recent_query = f"assigned_to eq '{assignee}' and state eq 'Closed'"
                recent_items = await self.search_client.hybrid_search(
                    query_text="*",
                    filters=recent_query,
                    top=50
                )
                
                load = TeamMemberLoad(
                    assignee=assignee,
                    active_items=len(active_items),
                    recent_assignments=len(recent_items),
                    avg_resolution_time=0.0,  # Could be calculated from recent items
                    expertise_areas=[]  # Could be inferred from work history
                )
                
                loads[assignee] = load
            
            # Update cache
            self._load_cache.update(loads)
            self._cache_timestamp = datetime.now()
            
            return loads
            
        except Exception as e:
            logger.error(f"Error getting team member loads: {e}")
            return {}
    
    def _calculate_confidence_score(self, candidate: AssignmentCandidate) -> float:
        """
        Calculate final confidence score for an assignment candidate.
        """
        try:
            # Base score from voting
            vote_score = min(candidate.vote_count / self.knn_k, 1.0)
            
            # Ownership bonus
            ownership_score = 1.0 if candidate.ownership_match else 0.0
            
            # Load penalty (inverted - lower load is better)
            load_score = 1.0 - candidate.load_penalty
            
            # Weighted combination
            confidence = (
                self.similarity_weight * vote_score +
                self.ownership_weight * ownership_score +
                self.load_balance_weight * load_score
            )
            
            return min(confidence, 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating confidence score: {e}")
            return 0.0
    
    def _select_best_candidate(self, candidates: List[AssignmentCandidate]) -> AssignmentCandidate:
        """
        Select the best assignment candidate from the list.
        """
        if not candidates:
            raise ValueError("No candidates provided")
        
        # Sort by confidence score (descending)
        candidates.sort(key=lambda x: x.confidence_score, reverse=True)
        
        return candidates[0]
