"""
Azure Functions App - AutoDefectTriage (Minimal Version)
Main function app configuration for Azure Functions v2 programming model.
This version has minimal dependencies to test if the functions can be detected.
"""

import azure.functions as func
import logging
import json

# Create the function app
app = func.FunctionApp()

# Configure logging
logging.basicConfig(level=logging.INFO)


@app.function_name(name="workitem_created")
@app.route(route="workitem_created", auth_level=func.AuthLevel.FUNCTION)
async def workitem_created_trigger(req: func.HttpRequest) -> func.HttpResponse:
    """
    HTTP trigger for Azure DevOps Service Hook when work items are created/updated.
    This is a minimal version for testing.
    """
    try:
        logging.info('WorkItem created function triggered')
        
        # Get the request body
        try:
            req_body = req.get_json()
            logging.info(f'Received payload: {json.dumps(req_body, indent=2) if req_body else "No JSON body"}')
        except Exception as e:
            logging.warning(f'Could not parse JSON body: {e}')
            req_body = None
        
        # Simple response for testing
        return func.HttpResponse(
            json.dumps({
                "status": "success",
                "message": "WorkItem function is working!",
                "received_data": bool(req_body)
            }),
            status_code=200,
            mimetype="application/json"
        )
        
    except Exception as e:
        logging.error(f"Error in workitem_created_trigger: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "status": "error",
                "message": f"Error processing request: {str(e)}"
            }),
            status_code=500,
            mimetype="application/json"
        )


@app.function_name(name="health_check")
@app.route(route="health", auth_level=func.AuthLevel.ANONYMOUS)
def health_check(req: func.HttpRequest) -> func.HttpResponse:
    """
    Simple health check endpoint.
    """
    return func.HttpResponse(
        json.dumps({
            "status": "healthy",
            "message": "Azure Functions app is running"
        }),
        status_code=200,
        mimetype="application/json"
    )


if __name__ == "__main__":
    print("Minimal function app created successfully")
    print(f"App type: {type(app)}")
    if hasattr(app, '_function_builders'):
        print(f"Function builders: {len(app._function_builders)}")
        for builder in app._function_builders:
            if hasattr(builder, '_function_name'):
                print(f"  - {builder._function_name}")
