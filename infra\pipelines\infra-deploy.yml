trigger:
  branches:
    include:
    - main
    - develop
  paths:
    include:
    - infra/*

variables:
  azureServiceConnection: 'azure-service-connection'
  resourceGroupName: 'rg-qa-ai-triage'
  location: 'East US'

stages:
- stage: ValidateInfrastructure
  displayName: 'Validate Infrastructure'
  jobs:
  - job: ValidateBicep
    displayName: 'Validate Bicep Templates'
    pool:
      vmImage: 'ubuntu-latest'
    steps:
    - task: AzureCLI@2
      displayName: 'Validate Bicep Template'
      inputs:
        azureSubscription: $(azureServiceConnection)
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          az bicep build --file infra/bicep/main.bicep
          az deployment group validate \
            --resource-group $(resourceGroupName) \
            --template-file infra/bicep/main.bicep \
            --parameters @infra/bicep/parameters.dev.json

- stage: DeployDev
  displayName: 'Deploy to Development'
  dependsOn: ValidateInfrastructure
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/develop'))
  jobs:
  - deployment: DeployInfraDev
    displayName: 'Deploy Infrastructure to Dev'
    pool:
      vmImage: 'ubuntu-latest'
    environment: 'development'
    strategy:
      runOnce:
        deploy:
          steps:
          - checkout: self
          - task: AzureCLI@2
            displayName: 'Create Resource Group'
            inputs:
              azureSubscription: $(azureServiceConnection)
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az group create --name $(resourceGroupName)-dev --location "$(location)"
          
          - task: AzureResourceManagerTemplateDeployment@3
            displayName: 'Deploy Bicep Template'
            inputs:
              deploymentScope: 'Resource Group'
              azureResourceManagerConnection: $(azureServiceConnection)
              subscriptionId: $(subscriptionId)
              action: 'Create Or Update Resource Group'
              resourceGroupName: '$(resourceGroupName)-dev'
              location: $(location)
              templateLocation: 'Linked artifact'
              csmFile: 'infra/bicep/main.bicep'
              csmParametersFile: 'infra/bicep/parameters.dev.json'
              deploymentMode: 'Incremental'

- stage: DeployProd
  displayName: 'Deploy to Production'
  dependsOn: ValidateInfrastructure
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
  jobs:
  - deployment: DeployInfraProd
    displayName: 'Deploy Infrastructure to Prod'
    pool:
      vmImage: 'ubuntu-latest'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - checkout: self
          - task: AzureCLI@2
            displayName: 'Create Resource Group'
            inputs:
              azureSubscription: $(azureServiceConnection)
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az group create --name $(resourceGroupName)-prod --location "$(location)"
          
          - task: AzureResourceManagerTemplateDeployment@3
            displayName: 'Deploy Bicep Template'
            inputs:
              deploymentScope: 'Resource Group'
              azureResourceManagerConnection: $(azureServiceConnection)
              subscriptionId: $(subscriptionId)
              action: 'Create Or Update Resource Group'
              resourceGroupName: '$(resourceGroupName)-prod'
              location: $(location)
              templateLocation: 'Linked artifact'
              csmFile: 'infra/bicep/main.bicep'
              csmParametersFile: 'infra/bicep/parameters.prod.json'
              deploymentMode: 'Incremental'
