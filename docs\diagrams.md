# QA AI Triage System - Architecture Diagrams

## System Overview

```mermaid
graph TB
    ADO[Azure DevOps] -->|Service Hook| AF[Azure Functions]
    AF -->|Query/Update| ADO
    AF -->|Search/Index| AS[Azure AI Search]
    AF -->|Notifications| Teams[Microsoft Teams]
    AF -->|Secrets| KV[Key Vault]
    AF -->|Logs/Metrics| AI[Application Insights]
    
    subgraph "AI Processing"
        AF --> EMB[Embeddings]
        AF --> DUP[Duplicate Detection]
        AF --> ASS[Assignment Engine]
        AF --> PRI[Priority Engine]
    end
    
    subgraph "Data Sources"
        CO[CODEOWNERS]
        HEU[Heuristics]
        HIST[Historical Data]
    end
    
    ASS --> CO
    ASS --> HEU
    ASS --> HIST
```

## Detailed Function Flow

```mermaid
sequenceDiagram
    participant ADO as Azure DevOps
    participant WH as Webhook
    participant AF as Azure Functions
    participant AS as AI Search
    participant EMB as Embedding Service
    participant DUP as Duplicate Detector
    participant ASSIGN as Assignment Engine
    participant PRI as Priority Engine
    participant TEAMS as Teams

    ADO->>WH: Work Item Created/Updated
    WH->>AF: HTTP POST with work item data
    
    AF->>EMB: Generate embeddings
    EMB-->>AF: Vector embeddings
    
    AF->>AS: Index work item with embeddings
    
    par Duplicate Detection
        AF->>DUP: Find duplicates
        DUP->>AS: Hybrid search
        AS-->>DUP: Similar items
        DUP-->>AF: Duplicate candidates
    and Assignment
        AF->>ASSIGN: Assign work item
        ASSIGN->>AS: Find similar assigned items
        AS-->>ASSIGN: Historical assignments
        ASSIGN-->>AF: Recommended assignee
    and Priority Calculation
        AF->>PRI: Calculate priority
        PRI-->>AF: Priority level
    end
    
    AF->>ADO: Update work item
    AF->>TEAMS: Send notification
    
    AF-->>WH: Success response
```

## AI Processing Pipeline

```mermaid
flowchart TD
    START([Work Item Input]) --> PARSE[Parse Work Item]
    PARSE --> EMB[Generate Embeddings]
    
    EMB --> DUP[Duplicate Detection]
    EMB --> ASSIGN[Assignment Engine]
    EMB --> PRI[Priority Engine]
    
    DUP --> SEARCH1[Hybrid Search]
    SEARCH1 --> FILTER1[Filter by Similarity]
    FILTER1 --> RANK1[Rank Duplicates]
    
    ASSIGN --> SEARCH2[Find Similar Items]
    SEARCH2 --> KNN[k-NN Voting]
    KNN --> OWNER[Code Ownership]
    OWNER --> HEUR[Heuristics]
    HEUR --> LOAD[Load Balancing]
    LOAD --> RANK2[Rank Candidates]
    
    PRI --> EXTRACT[Extract Features]
    EXTRACT --> RULES[Apply Rules]
    RULES --> SCORE[Calculate Score]
    SCORE --> LEVEL[Determine Level]
    
    RANK1 --> COMBINE[Combine Results]
    RANK2 --> COMBINE
    LEVEL --> COMBINE
    
    COMBINE --> UPDATE[Update Work Item]
    UPDATE --> NOTIFY[Send Notifications]
    NOTIFY --> END([Complete])
```

## Data Flow Architecture

```mermaid
graph LR
    subgraph "Input Sources"
        WI[Work Items]
        CO[CODEOWNERS]
        HIST[Historical Data]
        CONFIG[Configuration]
    end
    
    subgraph "Processing Layer"
        FUNC[Azure Functions]
        EMB[Embedding Service]
        ML[ML Models]
    end
    
    subgraph "Storage Layer"
        SEARCH[AI Search Index]
        KV[Key Vault]
        BLOB[Blob Storage]
    end
    
    subgraph "Output Targets"
        ADO[Azure DevOps]
        TEAMS[Teams Notifications]
        LOGS[Application Insights]
    end
    
    WI --> FUNC
    CO --> FUNC
    HIST --> FUNC
    CONFIG --> KV
    
    FUNC --> EMB
    FUNC --> ML
    
    EMB --> SEARCH
    ML --> SEARCH
    KV --> FUNC
    
    FUNC --> ADO
    FUNC --> TEAMS
    FUNC --> LOGS
    
    SEARCH --> FUNC
```

## Deployment Architecture

```mermaid
graph TB
    subgraph "Development"
        DEV_CODE[Source Code]
        DEV_BUILD[Build Pipeline]
        DEV_TEST[Unit Tests]
    end
    
    subgraph "Staging Environment"
        STAGE_INFRA[Staging Infrastructure]
        STAGE_FUNC[Staging Functions]
        STAGE_TEST[Integration Tests]
    end
    
    subgraph "Production Environment"
        PROD_INFRA[Production Infrastructure]
        PROD_FUNC[Production Functions]
        PROD_MONITOR[Monitoring]
    end
    
    subgraph "Infrastructure as Code"
        BICEP[Bicep Templates]
        PARAMS[Parameter Files]
        PIPELINE[Deployment Pipeline]
    end
    
    DEV_CODE --> DEV_BUILD
    DEV_BUILD --> DEV_TEST
    DEV_TEST --> STAGE_FUNC
    
    BICEP --> STAGE_INFRA
    PARAMS --> STAGE_INFRA
    STAGE_FUNC --> STAGE_TEST
    
    STAGE_TEST --> PROD_FUNC
    BICEP --> PROD_INFRA
    PROD_FUNC --> PROD_MONITOR
    
    PIPELINE --> STAGE_INFRA
    PIPELINE --> PROD_INFRA
```

## Security Architecture

```mermaid
graph TB
    subgraph "External"
        USER[Users]
        ADO[Azure DevOps]
        TEAMS[Microsoft Teams]
    end
    
    subgraph "Azure Boundary"
        subgraph "Network Security"
            WAF[Web Application Firewall]
            NSG[Network Security Groups]
        end
        
        subgraph "Identity & Access"
            AAD[Azure Active Directory]
            MSI[Managed Service Identity]
            RBAC[Role-Based Access Control]
        end
        
        subgraph "Data Protection"
            KV[Key Vault]
            SSL[SSL/TLS Encryption]
            ENCRYPT[Encryption at Rest]
        end
        
        subgraph "Application Layer"
            FUNC[Azure Functions]
            SEARCH[AI Search]
            AI[Application Insights]
        end
    end
    
    USER --> WAF
    ADO --> WAF
    TEAMS --> WAF
    
    WAF --> NSG
    NSG --> FUNC
    
    FUNC --> MSI
    MSI --> AAD
    AAD --> RBAC
    
    FUNC --> KV
    FUNC --> SEARCH
    FUNC --> AI
    
    KV --> ENCRYPT
    SEARCH --> ENCRYPT
    AI --> ENCRYPT
    
    FUNC --> SSL
    SEARCH --> SSL
```

## Monitoring and Observability

```mermaid
graph TB
    subgraph "Application"
        FUNC[Azure Functions]
        SEARCH[AI Search]
        KV[Key Vault]
    end
    
    subgraph "Monitoring Stack"
        AI[Application Insights]
        LOGS[Log Analytics]
        METRICS[Azure Metrics]
    end
    
    subgraph "Alerting"
        ALERTS[Azure Alerts]
        ACTION[Action Groups]
        NOTIFY[Notifications]
    end
    
    subgraph "Dashboards"
        PORTAL[Azure Portal]
        WORKBOOK[Azure Workbooks]
        GRAFANA[Grafana]
    end
    
    FUNC --> AI
    SEARCH --> METRICS
    KV --> LOGS
    
    AI --> ALERTS
    METRICS --> ALERTS
    LOGS --> ALERTS
    
    ALERTS --> ACTION
    ACTION --> NOTIFY
    
    AI --> PORTAL
    METRICS --> WORKBOOK
    LOGS --> GRAFANA
```

## Disaster Recovery

```mermaid
graph LR
    subgraph "Primary Region"
        P_FUNC[Functions]
        P_SEARCH[AI Search]
        P_KV[Key Vault]
        P_AI[App Insights]
    end
    
    subgraph "Secondary Region"
        S_FUNC[Functions]
        S_SEARCH[AI Search]
        S_KV[Key Vault]
        S_AI[App Insights]
    end
    
    subgraph "Global Services"
        TM[Traffic Manager]
        DNS[DNS]
        BACKUP[Backup Storage]
    end
    
    P_FUNC -.->|Geo-replication| S_FUNC
    P_SEARCH -.->|Backup/Restore| S_SEARCH
    P_KV -.->|Geo-replication| S_KV
    P_AI -.->|Export| BACKUP
    
    TM --> P_FUNC
    TM -.->|Failover| S_FUNC
    DNS --> TM
    
    BACKUP -.->|Restore| S_AI
```
