#!/usr/bin/env python3
"""
Test script to verify Azure Functions imports work correctly.
This simulates how Azure Functions Core Tools would import the modules.
"""

import sys
import os
import importlib.util

def test_azure_functions_import():
    """Test importing the function app as Azure Functions would."""
    print("🧪 Testing Azure Functions Import Simulation")
    print("=" * 50)
    
    # Add the current directory to Python path (simulating Azure Functions behavior)
    current_dir = os.getcwd()
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    print(f"📁 Working directory: {current_dir}")
    print(f"🐍 Python version: {sys.version}")
    print()
    
    try:
        # Test 1: Import the main function app
        print("1️⃣ Testing function app import...")
        from __app__.function_app import app
        print("✅ Function app imported successfully")
        print(f"   App type: {type(app)}")
        print()
        
        # Test 2: Test the workitem handler import
        print("2️⃣ Testing workitem handler import...")
        from __app__.workitem_created.handler import process_workitem_webhook
        print("✅ Workitem handler imported successfully")
        print(f"   Handler type: {type(process_workitem_webhook)}")
        print()
        
        # Test 3: Test backfill job import
        print("3️⃣ Testing backfill job import...")
        from __app__.backfill_job import main as backfill_main
        print("✅ Backfill job imported successfully")
        print(f"   Backfill main type: {type(backfill_main)}")
        print()
        
        # Test 4: Test all common modules
        print("4️⃣ Testing common modules...")
        from __app__.common.models.schemas import WorkItem, TriageResult
        from __app__.common.adapters.ado_client import AdoClient
        from __app__.common.ai.duplicate import DuplicateDetector
        from __app__.common.utils.config import get_config
        print("✅ All common modules imported successfully")
        print()
        
        # Test 5: Test creating instances
        print("5️⃣ Testing object creation...")
        
        # Test WorkItem creation
        work_item_data = {
            'id': 123,
            'title': 'Test Work Item',
            'description': 'Test description',
            'work_item_type': 'Bug',
            'state': 'New',
            'area_path': 'Test\\Area',
            'iteration_path': 'Test\\Iteration',
            'assigned_to': '',
            'created_by': 'Test User',
            'created_date': '2024-01-01T00:00:00Z',
            'changed_date': '2024-01-01T00:00:00Z',
            'priority': 2,
            'severity': 'High',
            'tags': 'test',
            'repro_steps': 'Test steps',
            'system_info': 'Test system'
        }
        
        work_item = WorkItem(**work_item_data)
        print(f"✅ WorkItem created: {work_item.title}")
        
        # Test config loading
        config = get_config()
        print(f"✅ Config loaded: {type(config)}")
        print()
        
        print("🎉 All tests passed! The '__app__.common' import issue is resolved.")
        print()
        print("💡 Summary:")
        print("   ✅ Function app can be imported")
        print("   ✅ All handlers can be imported") 
        print("   ✅ All common modules work")
        print("   ✅ Objects can be created")
        print("   ✅ Configuration can be loaded")
        print()
        print("🚀 The Azure Functions should now start without import errors.")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        import traceback
        print(f"   Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_azure_functions_import()
    if success:
        print("\n✅ Ready to run Azure Functions!")
    else:
        print("\n❌ Fix the import issues before running Azure Functions.")
    
    sys.exit(0 if success else 1)
